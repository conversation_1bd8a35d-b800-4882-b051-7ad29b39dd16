package com.caidaocloud.open.auth.service.infrastructure.config.oauth;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.caidaocloud.open.auth.service.domain.entity.OAuthClient;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.oauth2.common.DefaultOAuth2AccessToken;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.config.annotation.configurers.ClientDetailsServiceConfigurer;
import org.springframework.security.oauth2.config.annotation.web.configuration.AuthorizationServerConfigurerAdapter;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableAuthorizationServer;
import org.springframework.security.oauth2.config.annotation.web.configurers.AuthorizationServerEndpointsConfigurer;
import org.springframework.security.oauth2.config.annotation.web.configurers.AuthorizationServerSecurityConfigurer;
import org.springframework.security.oauth2.provider.ClientDetailsService;
import org.springframework.security.oauth2.provider.CompositeTokenGranter;
import org.springframework.security.oauth2.provider.OAuth2RequestFactory;
import org.springframework.security.oauth2.provider.TokenGranter;
import org.springframework.security.oauth2.provider.TokenRequest;
import org.springframework.security.oauth2.provider.approval.TokenApprovalStore;
import org.springframework.security.oauth2.provider.client.ClientCredentialsTokenGranter;
import org.springframework.security.oauth2.provider.code.AuthorizationCodeServices;
import org.springframework.security.oauth2.provider.code.AuthorizationCodeTokenGranter;
import org.springframework.security.oauth2.provider.implicit.ImplicitTokenGranter;
import org.springframework.security.oauth2.provider.password.ResourceOwnerPasswordTokenGranter;
import org.springframework.security.oauth2.provider.refresh.RefreshTokenGranter;
import org.springframework.security.oauth2.provider.token.AuthorizationServerTokenServices;
import org.springframework.security.oauth2.provider.token.DefaultTokenServices;
import org.springframework.security.oauth2.provider.token.TokenEnhancer;
import org.springframework.security.oauth2.provider.token.TokenEnhancerChain;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.security.oauth2.provider.token.store.JwtAccessTokenConverter;
import org.springframework.security.oauth2.provider.token.store.JwtTokenStore;
import org.springframework.security.oauth2.provider.token.store.redis.RedisTokenStore;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

/**
 * OAuth2 Authorization Server Configuration (Client Credentials Only)
 *
 * <AUTHOR> Zhou
 */
@Configuration
@EnableAuthorizationServer
public class OAuth2AuthorizationServerConfig extends AuthorizationServerConfigurerAdapter {

    /**
     * 统一配置认证请求前缀
     */
    @Value(value = "${caidaocloud.oauth.pathmapping.prefix:/api/open/v1}")
    private String oauthPathPrefix;

    @Autowired
    private RedisConnectionFactory redisConnectionFactory;

    @Value("${oauth2.jwt.access-token-validity-seconds:600}")
    private int accessTokenValiditySeconds;

    @Value("${oauth2.jwt.refresh-token-validity-seconds:2592000}")
    private int refreshTokenValiditySeconds;

    @Autowired
    private OAuthClientDetailService oAuthClientDetailService;

    @Autowired
    private JwtAccessTokenConverter jwtAccessTokenConverter;

    @Autowired
    private OAuthClientExceptionEntryPoint authenticationEntryPoint;
    /**
     * Configure client details service
     */
    @Override
    public void configure(ClientDetailsServiceConfigurer clients) throws Exception {
        clients.withClientDetails(oAuthClientDetailService);
    }

    /**
     * Configure authorization server endpoints
     * Only support client_credentials grant type
     */
    @Override
    public void configure(AuthorizationServerEndpointsConfigurer endpoints) throws Exception {
        TokenEnhancerChain tokenEnhancerChain = new TokenEnhancerChain();
        tokenEnhancerChain.setTokenEnhancers(Arrays.asList(tokenEnhancer(), jwtAccessTokenConverter));

        endpoints
                .accessTokenConverter(jwtAccessTokenConverter)
                .tokenServices(tokenServices())
                .reuseRefreshTokens(false)
                .pathMapping("/oauth/authorize", oauthPathPrefix + "/oauth/authorize")
                // 修改客户端认证的默认 path
                .pathMapping("/oauth/token", oauthPathPrefix + "/oauth/token")
                .pathMapping("/oauth/confirm_access", oauthPathPrefix + "/oauth/confirm_access")
                .pathMapping("/oauth/error", oauthPathPrefix + "/oauth/error")
                .pathMapping("/oauth/check_token", oauthPathPrefix + "/oauth/check_token")
                .pathMapping("/oauth/token_key", oauthPathPrefix + "/oauth/token_key");

        endpoints.tokenGranter(new CompositeTokenGranter(getDefaultTokenGranters(endpoints)));
        // Only allow client_credentials grant type
        // .allowedTokenEndpointRequestMethods(org.springframework.http.HttpMethod.POST);
    }


    private List<TokenGranter> getDefaultTokenGranters(AuthorizationServerEndpointsConfigurer endpoints) {
        ClientDetailsService clientDetails = endpoints.getClientDetailsService();
        AuthorizationServerTokenServices tokenServices = tokenServices();
        AuthorizationCodeServices authorizationCodeServices = endpoints.getAuthorizationCodeServices();
        OAuth2RequestFactory requestFactory = endpoints.getOAuth2RequestFactory();

        List<TokenGranter> tokenGranters = new ArrayList<TokenGranter>();
        tokenGranters.add(new AuthorizationCodeTokenGranter(tokenServices, authorizationCodeServices, clientDetails,
                requestFactory));
        tokenGranters.add(new RefreshTokenGranter(tokenServices, clientDetails, requestFactory));
        ClientCredentialsTokenGranter tokenGranter = new ClientCredentialsTokenGranter(tokenServices, clientDetails, requestFactory);
        tokenGranter.setAllowRefresh(true);
        tokenGranters.add(tokenGranter);
        return tokenGranters;
    }

    /**
     * Configure authorization server security
     */
    @Override
    public void configure(AuthorizationServerSecurityConfigurer security) throws Exception {
        OAuthClientCredentialsTokenEndpointFilter endpointFilter = new OAuthClientCredentialsTokenEndpointFilter(security);
        endpointFilter.afterPropertiesSet();
        endpointFilter.setAuthenticationEntryPoint(authenticationEntryPoint);
        security.addTokenEndpointAuthenticationFilter(endpointFilter);
        security
                .tokenKeyAccess("permitAll()")
                // .checkTokenAccess("isAuthenticated()")
                .checkTokenAccess("permitAll()")
                .allowFormAuthenticationForClients();
    }

    /**
     * Token store configuration using Redis
     */
    @Bean
    public TokenStore tokenStore() {
        return new RedisTokenStore(redisConnectionFactory);
        // JwtTokenStore store = new JwtTokenStore(jwtAccessTokenConverter);
        // TokenApprovalStore approvalStore = new TokenApprovalStore();
        // approvalStore.setTokenStore(new RedisTokenStore(redisConnectionFactory));
        // store.setApprovalStore(approvalStore);
        // return store;
    }

    /**
     * Custom Token Enhancer for client credentials
     */
    @Bean
    public TokenEnhancer tokenEnhancer() {
        return (accessToken, authentication) -> {
            final Map<String, Object> additionalInfo = new HashMap<>(10);
            additionalInfo.put("userid", 0);
            additionalInfo.put("empid", 0);
            if (authentication.getUserAuthentication() != null) {
                // UserDetailDto user = (UserDetailDto)
                // authentication.getUserAuthentication().getPrincipal();
                // if (user != null) {
                // additionalInfo.put("tenantId", user.getTenantId());
                // additionalInfo.put("belongId", user.getBelongId());
                // }
            } else {
                OAuthClient oauthClient = oAuthClientDetailService
                        .loadClientById(authentication.getOAuth2Request().getClientId());
                additionalInfo.put("tenantId", oauthClient.getTenantId());
                // additionalInfo.put("authUrl",
                // StringUtils.isNotBlank(oauthClient.getAuthUrl()) ? oauthClient.getAuthUrl() :
                // "");
            }
            ((DefaultOAuth2AccessToken) accessToken).setAdditionalInformation(additionalInfo);
            return accessToken;
        };
    }

    /**
     * Token Services Configuration
     */
    @Bean
    @Primary
    public DefaultTokenServices tokenServices() {
        TokenEnhancerChain tokenEnhancerChain = new TokenEnhancerChain();
        tokenEnhancerChain.setTokenEnhancers(Arrays.asList(tokenEnhancer(), jwtAccessTokenConverter));
        DefaultTokenServices tokenServices = new DefaultTokenServices();
        tokenServices.setTokenStore(tokenStore());
        tokenServices.setSupportRefreshToken(true);
        tokenServices.setReuseRefreshToken(false);
        tokenServices.setAccessTokenValiditySeconds(accessTokenValiditySeconds);
        tokenServices.setRefreshTokenValiditySeconds(refreshTokenValiditySeconds);
        tokenServices.setTokenEnhancer(tokenEnhancerChain);
        return tokenServices;
    }


}
