<component name="ProjectCodeStyleConfiguration">
  <code_scheme name="Project" version="173">
    <option name="OTHER_INDENT_OPTIONS">
      <value>
        <option name="USE_TAB_CHARACTER" value="true" />
      </value>
    </option>
    <option name="FORMATTER_TAGS_ENABLED" value="true" />
    <GroovyCodeStyleSettings>
      <option name="USE_FLYING_GEESE_BRACES" value="true" />
      <option name="CLASS_COUNT_TO_USE_IMPORT_ON_DEMAND" value="99" />
      <option name="NAMES_COUNT_TO_USE_IMPORT_ON_DEMAND" value="99" />
      <option name="IMPORT_LAYOUT_TABLE">
        <value>
          <package name="java" withSubpackages="true" static="false" />
          <emptyLine />
          <package name="javax" withSubpackages="true" static="false" />
          <emptyLine />
          <package name="" withSubpackages="true" static="false" />
          <emptyLine />
          <package name="org.springframework" withSubpackages="true" static="false" />
          <emptyLine />
          <package name="" withSubpackages="true" static="true" />
        </value>
      </option>
    </GroovyCodeStyleSettings>
    <JavaCodeStyleSettings>
      <option name="TEST_NAME_SUFFIX" value="Tests" />
      <option name="VISIBILITY" value="packageLocal" />
      <option name="CLASS_NAMES_IN_JAVADOC" value="3" />
      <option name="CLASS_COUNT_TO_USE_IMPORT_ON_DEMAND" value="99" />
      <option name="NAMES_COUNT_TO_USE_IMPORT_ON_DEMAND" value="99" />
      <option name="PACKAGES_TO_USE_IMPORT_ON_DEMAND">
        <value />
      </option>
      <option name="IMPORT_LAYOUT_TABLE">
        <value>
          <package name="java" withSubpackages="true" static="false" />
          <emptyLine />
          <package name="javax" withSubpackages="true" static="false" />
          <emptyLine />
          <package name="" withSubpackages="true" static="false" />
          <emptyLine />
          <package name="org.springframework" withSubpackages="true" static="false" />
          <emptyLine />
          <package name="" withSubpackages="true" static="true" />
        </value>
      </option>
      <option name="ENABLE_JAVADOC_FORMATTING" value="false" />
    </JavaCodeStyleSettings>
    <JetCodeStyleSettings>
      <option name="CODE_STYLE_DEFAULTS" value="KOTLIN_OFFICIAL" />
    </JetCodeStyleSettings>
    <ScalaCodeStyleSettings>
      <option name="MULTILINE_STRING_CLOSING_QUOTES_ON_NEW_LINE" value="true" />
    </ScalaCodeStyleSettings>
    <codeStyleSettings language="Groovy">
      <option name="RIGHT_MARGIN" value="120" />
      <option name="KEEP_BLANK_LINES_BEFORE_RBRACE" value="1" />
      <option name="ELSE_ON_NEW_LINE" value="true" />
      <option name="WHILE_ON_NEW_LINE" value="true" />
      <option name="CATCH_ON_NEW_LINE" value="true" />
      <option name="FINALLY_ON_NEW_LINE" value="true" />
      <option name="INDENT_CASE_FROM_SWITCH" value="false" />
      <option name="ALIGN_MULTILINE_CHAINED_METHODS" value="true" />
      <option name="ALIGN_MULTILINE_PARAMETERS" value="false" />
      <option name="ALIGN_MULTILINE_METHOD_BRACKETS" value="true" />
      <option name="SPACE_BEFORE_ARRAY_INITIALIZER_LBRACE" value="true" />
      <option name="PREFER_PARAMETERS_WRAP" value="true" />
      <option name="KEEP_MULTIPLE_EXPRESSIONS_IN_ONE_LINE" value="true" />
      <option name="IF_BRACE_FORCE" value="3" />
      <option name="WHILE_BRACE_FORCE" value="3" />
      <option name="FOR_BRACE_FORCE" value="3" />
      <option name="WRAP_ON_TYPING" value="0" />
      <indentOptions>
        <option name="USE_TAB_CHARACTER" value="true" />
        <option name="LABEL_INDENT_SIZE" value="4" />
      </indentOptions>
    </codeStyleSettings>
    <codeStyleSettings language="JAVA">
      <option name="LINE_COMMENT_AT_FIRST_COLUMN" value="false" />
      <option name="LINE_COMMENT_ADD_SPACE" value="true" />
      <option name="KEEP_BLANK_LINES_BEFORE_RBRACE" value="1" />
      <option name="ELSE_ON_NEW_LINE" value="true" />
      <option name="WHILE_ON_NEW_LINE" value="true" />
      <option name="CATCH_ON_NEW_LINE" value="true" />
      <option name="FINALLY_ON_NEW_LINE" value="true" />
      <option name="INDENT_CASE_FROM_SWITCH" value="false" />
      <option name="ALIGN_MULTILINE_PARAMETERS" value="false" />
      <option name="SPACE_WITHIN_BRACES" value="true" />
      <option name="SPACE_BEFORE_ARRAY_INITIALIZER_LBRACE" value="true" />
      <option name="METHOD_CALL_CHAIN_WRAP" value="1" />
      <option name="BINARY_OPERATION_SIGN_ON_NEXT_LINE" value="true" />
      <option name="KEEP_SIMPLE_CLASSES_IN_ONE_LINE" value="true" />
      <option name="KEEP_MULTIPLE_EXPRESSIONS_IN_ONE_LINE" value="true" />
      <indentOptions>
        <option name="USE_TAB_CHARACTER" value="true" />
      </indentOptions>
    </codeStyleSettings>
    <codeStyleSettings language="XML">
      <indentOptions>
        <option name="USE_TAB_CHARACTER" value="true" />
      </indentOptions>
    </codeStyleSettings>
    <codeStyleSettings language="kotlin">
      <option name="CODE_STYLE_DEFAULTS" value="KOTLIN_OFFICIAL" />
    </codeStyleSettings>
  </code_scheme>
</component>