package com.caidaocloud.open.auth.service.infrastructure.config.aliyun;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

/**
 * Alibaba Cloud API Gateway Configuration
 * 
 * <AUTHOR>
 */
@Configuration
public class AliyunConfig {

    @Value("${aliyun.access-key-id}")
    private String accessKeyId;

    @Value("${aliyun.access-key-secret}")
    private String accessKeySecret;

    @Value("${aliyun.endpoint}")
    private String endpoint;

    /**
     * Rest template for API Gateway calls
     */
    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }

    public String getAccessKeyId() {
        return accessKeyId;
    }

    public String getAccessKeySecret() {
        return accessKeySecret;
    }

    public String getEndpoint() {
        return endpoint;
    }

}
