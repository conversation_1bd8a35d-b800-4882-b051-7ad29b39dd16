package com.caidaocloud.open.auth.service.infrastructure.util;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

import org.springframework.util.Base64Utils;

public class SHA256Util {

    public static String base64Encode(String input) throws NoSuchAlgorithmException {
        try {
            // 获取SHA-256算法的MessageDigest实例
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            
            // 将输入字符串转换为字节数组并计算哈希
            byte[] hash = digest.digest(input.getBytes(StandardCharsets.UTF_8));

            return Base64Utils.encodeToString(hash);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("SHA-256算法不可用", e);
        }
    }

    public static String base64Encode(byte[] bytes) throws NoSuchAlgorithmException {
        try {
            // 获取SHA-256算法的MessageDigest实例
            MessageDigest digest = MessageDigest.getInstance("SHA-256");

            // 将输入字符串转换为字节数组并计算哈希
            byte[] hash = digest.digest(bytes);

            return Base64Utils.encodeToString(hash);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("SHA-256算法不可用", e);
        }
    }
}

