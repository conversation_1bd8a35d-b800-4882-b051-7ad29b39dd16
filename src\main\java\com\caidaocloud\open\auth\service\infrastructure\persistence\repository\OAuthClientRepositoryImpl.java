package com.caidaocloud.open.auth.service.infrastructure.persistence.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.caidaocloud.open.auth.service.domain.entity.OAuthClient;
import com.caidaocloud.open.auth.service.domain.repository.OAuthClientRepository;
import com.caidaocloud.open.auth.service.infrastructure.persistence.po.OAuthClientPo;
import com.caidaocloud.open.auth.service.infrastructure.persistence.mapper.OAuthClientMapper;
import com.caidaocloud.util.ObjectConverter;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * OAuth Client Repository Implementation (Infrastructure Layer)
 * 
 * <AUTHOR>
 */
@Repository
public class OAuthClientRepositoryImpl implements OAuthClientRepository {

    @Autowired
    private OAuthClientMapper clientMapper;

    @Override
    public Optional<OAuthClient> findByClientId(String clientId) {
        LambdaQueryWrapper<OAuthClientPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OAuthClientPo::getClientId, clientId);
        OAuthClientPo entity = clientMapper.selectOne(queryWrapper);
        return Optional.ofNullable(entity).map(this::toDomainModel);
    }

    /**
     * Convert entity to domain model
     */
    private OAuthClient toDomainModel(OAuthClientPo entity) {
        return ObjectConverter.convert(entity, OAuthClient.class);
    }

    /**
     * Convert domain model to entity
     */
    private OAuthClientPo toEntity(OAuthClient client) {
        return ObjectConverter.convert(client, OAuthClientPo.class);
    }
}
