package com.caidaocloud.open.auth.service.infrastructure.persistence.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * OAuth Client po
 * 
 * <AUTHOR>
 */
@TableName("oauth_client")
@Data
public class OAuthClientPo {

    private String clientId;

    private String clientSecret;

    private String scope;

    private String authorities;
    private String authorizedGrantTypes;

    private Integer accessTokenValidity;
    //
    // private String clientName;
    //
    // private String clientDescription;

    // private Boolean enabled = true;

    private String tenantId;

}
