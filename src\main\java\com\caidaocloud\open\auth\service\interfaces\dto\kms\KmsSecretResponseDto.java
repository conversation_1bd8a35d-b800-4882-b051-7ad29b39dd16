package com.caidaocloud.open.auth.service.interfaces.dto.kms;

import lombok.Data;

/**
 * KMS密钥管理通用响应DTO
 * 只保留与凭证业务相关的核心字段
 * 
 * <AUTHOR>
 * @date 2025/9/5
 */
@Data
public class KmsSecretResponseDto {

    /**
     * 密钥名称
     */
    private String secretName;

    /**
     * 版本ID
     */
    private String versionId;

    /**
     * 密钥数据（仅在获取密钥值时返回）
     */
    private String secretData;

    /**
     * 创建成功响应
     */
    public static KmsSecretResponseDto createSuccess(String secretName, String versionId) {
        KmsSecretResponseDto response = new KmsSecretResponseDto();
        response.setSecretName(secretName);
        response.setVersionId(versionId);
        return response;
    }

    /**
     * 获取密钥值成功响应
     */
    public static KmsSecretResponseDto getSuccess(String secretName, String versionId, String secretData) {
        KmsSecretResponseDto response = new KmsSecretResponseDto();
        response.setSecretName(secretName);
        response.setVersionId(versionId);
        response.setSecretData(secretData);
        return response;
    }

    /**
     * 更新密钥值成功响应
     */
    public static KmsSecretResponseDto putSuccess(String secretName, String versionId) {
        KmsSecretResponseDto response = new KmsSecretResponseDto();
        response.setSecretName(secretName);
        response.setVersionId(versionId);
        return response;
    }

    /**
     * 操作失败响应
     */
    public static KmsSecretResponseDto failure(String secretName, String message) {
        KmsSecretResponseDto response = new KmsSecretResponseDto();
        response.setSecretName(secretName);
        return response;
    }
}
