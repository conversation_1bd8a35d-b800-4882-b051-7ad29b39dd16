package com.caidaocloud.open.auth.service.infrastructure.config.aliyun;

import java.util.Map;

import com.caidaocloud.open.auth.service.OpenAuthApplication;
import com.googlecode.totallylazy.Maps;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * KmsJwtSigner
 *
 * <AUTHOR>
 */
@SpringBootTest(classes = OpenAuthApplication.class)
@RunWith(SpringJUnit4ClassRunner.class)
public class KmsJwtSignerTest {

    @Autowired
    private  KmsJwtSigner kmsJwtSigner;


    @Test
    public void testDevConfigurationConstants() {
        Map<String, Object> name = Maps.map("name", "123");
        System.out.println(kmsJwtSigner.sign(name));
    }


}
