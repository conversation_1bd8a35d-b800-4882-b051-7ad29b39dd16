# KMS密钥管理API文档

## 概述

本文档描述了基于阿里云KMS（Key Management Service）的密钥管理API接口。这些接口提供了创建、获取和更新密钥的功能，并包含完整的参数校验。

## DTO整合说明

为了简化API设计和减少代码冗余，我们将原来的6个独立DTO类整合为2个通用DTO：

### 请求DTO整合
- **KmsSecretRequestDto**: 统一的请求DTO，支持所有三种操作
  - 包含参数校验方法：`validateForCreate()` 和 `validateForPut()`
  - 根据不同操作类型进行相应的参数验证

### 响应DTO整合
- **KmsSecretResponseDto**: 统一的响应DTO，只保留核心业务字段
  - 移除了与凭证业务无关的字段（如requestId、arn、createTime等）
  - 提供静态工厂方法创建不同类型的响应
  - 包含操作成功/失败状态和消息

## API接口

### 1. 创建密钥

**接口地址：** `POST /api/open/v1/kms/secret/create`

**请求参数：**
```json
{
  "secretName": "my-secret",
  "versionId": "v1",
  "secretData": "my-secret-data"
}
```

**参数说明：**
- `secretName`: 密钥名称，必填，长度1-192个字符
- `versionId`: 版本ID，必填，长度1-64个字符
- `secretData`: 密钥数据，必填，最大长度65536个字符

**响应示例：**
```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    "secretName": "my-secret",
    "versionId": "v1",
    "success": true,
    "message": "密钥创建成功"
  }
}
```

### 2. 获取密钥值

**接口地址：** `POST /api/open/v1/kms/secret/getValue`

**请求参数：**
```json
{
  "secretName": "my-secret"
}
```

**参数说明：**
- `secretName`: 密钥名称，必填，长度1-192个字符

**响应示例：**
```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    "secretName": "my-secret",
    "versionId": "v1",
    "secretData": "my-secret-data",
    "success": true,
    "message": "密钥获取成功"
  }
}
```

### 3. 更新密钥值

**接口地址：** `POST /api/open/v1/kms/secret/putValue`

**请求参数：**
```json
{
  "versionId": "v2",
  "secretName": "my-secret",
  "secretData": "updated-secret-data"
}
```

**参数说明：**
- `versionId`: 版本ID，必填，长度1-64个字符
- `secretName`: 密钥名称，必填，长度1-192个字符
- `secretData`: 密钥数据，必填，最大长度65536个字符

**响应示例：**
```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    "secretName": "my-secret",
    "versionId": "v2",
    "success": true,
    "message": "密钥更新成功"
  }
}
```

## 错误处理

### 参数校验错误

当请求参数不符合校验规则时，会返回400状态码和详细的错误信息：

```json
{
  "success": false,
  "message": "参数校验失败: secretName: 密钥名称不能为空; versionId: 版本ID不能为空"
}
```

### 系统错误

当系统发生异常时，会返回500状态码和错误信息：

```json
{
  "success": false,
  "message": "创建密钥失败: KMS服务异常"
}
```

## 配置要求

### 1. 阿里云配置

在`application.yml`中配置阿里云访问凭证：

```yaml
aliyun:
  access-key-id: ${ALIYUN_ACCESS_KEY_ID}
  access-key-secret: ${ALIYUN_ACCESS_KEY_SECRET}
  endpoint: ${ALIYUN_KMS_ENDPOINT:kms.cn-shanghai.aliyuncs.com}
```

### 2. 环境变量

设置以下环境变量：

```bash
ALIYUN_ACCESS_KEY_ID=your-access-key-id
ALIYUN_ACCESS_KEY_SECRET=your-access-key-secret
ALIYUN_KMS_ENDPOINT=kms.cn-shanghai.aliyuncs.com
```

## 使用示例

### cURL示例

```bash
# 创建密钥
curl -X POST http://localhost:8080/api/open/v1/kms/secret/create \
  -H "Content-Type: application/json" \
  -d '{
    "secretName": "my-test-secret",
    "versionId": "v1",
    "secretData": "my-secret-data"
  }'

# 获取密钥值
curl -X POST http://localhost:8080/api/open/v1/kms/secret/getValue \
  -H "Content-Type: application/json" \
  -d '{
    "secretName": "my-test-secret"
  }'

# 更新密钥值
curl -X POST http://localhost:8080/api/open/v1/kms/secret/putValue \
  -H "Content-Type: application/json" \
  -d '{
    "versionId": "v2",
    "secretName": "my-test-secret",
    "secretData": "updated-secret-data"
  }'
```

## 安全建议

1. **访问控制**: 确保只有授权的应用程序可以访问这些API
2. **HTTPS**: 在生产环境中使用HTTPS传输
3. **密钥轮换**: 定期轮换阿里云访问密钥
4. **日志审计**: 监控API调用日志，及时发现异常访问
5. **权限最小化**: 为阿里云RAM用户分配最小必要权限

## 测试

### 编译项目

```bash
mvn compile
```

### 运行应用

```bash
mvn spring-boot:run
```

### API测试脚本

使用提供的测试脚本：

```bash
# 给脚本执行权限
chmod +x test-kms-api.sh

# 运行测试（默认localhost:8080）
./test-kms-api.sh

# 或指定其他地址
./test-kms-api.sh http://your-server:8080
```

## 故障排除

### 常见问题

1. **KMS服务连接失败**
   - 检查网络连接
   - 验证阿里云访问凭证
   - 确认KMS服务地域设置

2. **参数校验失败**
   - 检查请求参数格式
   - 确认参数长度限制
   - 验证必填字段

3. **权限不足**
   - 检查RAM用户权限
   - 确认KMS密钥访问权限
   - 验证资源访问策略
