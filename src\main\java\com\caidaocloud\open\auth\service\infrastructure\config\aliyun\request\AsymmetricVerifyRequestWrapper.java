package com.caidaocloud.open.auth.service.infrastructure.config.aliyun.request;

import com.aliyun.kms20160120.Client;
import com.aliyun.kms20160120.models.AsymmetricVerifyRequest;
import com.aliyun.kms20160120.models.AsymmetricVerifyResponse;

/**
 * 非对称验签请求包装器
 */
public class AsymmetricVerifyRequestWrapper extends KmsRequestWrapper<AsymmetricVerifyRequest, AsymmetricVerifyResponse> {
    public AsymmetricVerifyRequestWrapper(Client kmsClient) {
        super(kmsClient, "asymmetricVerify");
    }


}
