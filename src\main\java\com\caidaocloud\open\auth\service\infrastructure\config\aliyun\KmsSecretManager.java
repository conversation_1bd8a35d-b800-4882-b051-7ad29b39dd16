package com.caidaocloud.open.auth.service.infrastructure.config.aliyun;

import com.aliyun.kms20160120.Client;
import com.aliyun.kms20160120.models.CreateSecretRequest;
import com.aliyun.kms20160120.models.CreateSecretResponseBody;
import com.aliyun.kms20160120.models.GetSecretValueRequest;
import com.aliyun.kms20160120.models.GetSecretValueResponseBody;
import com.aliyun.kms20160120.models.PutSecretValueRequest;
import com.aliyun.kms20160120.models.PutSecretValueResponseBody;
import com.aliyun.teaopenapi.models.Config;
import com.caidaocloud.open.auth.service.infrastructure.config.aliyun.request.CreateSecretRequestWrapper;
import com.caidaocloud.open.auth.service.infrastructure.config.aliyun.request.GetSecretValueRequestWrapper;
import com.caidaocloud.open.auth.service.infrastructure.config.aliyun.request.PutSecretValueRequestWrapper;

/**
 * kms凭证管理
 * <AUTHOR>
 * @date 2025/9/4
 */
public class KmsSecretManager {
	private final Client kmsClient;

	public KmsSecretManager(AliyunConfig aliyunConfig) throws Exception {
		Config config = new Config()
				.setAccessKeyId(aliyunConfig.getAccessKeyId())
				.setAccessKeySecret(aliyunConfig.getAccessKeySecret())
				.setEndpoint(aliyunConfig.getEndpoint());
		this.kmsClient = new Client(config);
	}

	public CreateSecretResponseBody createSecret(String secretName, String versionId, String secretData) {
		CreateSecretRequestWrapper wrapper = new CreateSecretRequestWrapper(kmsClient);
		CreateSecretRequest request = wrapper.builder().build()
				.setSecretName(secretName)
				.setVersionId(versionId)
				.setSecretData(secretData);
		return wrapper.execute(request).getBody();
	}

	public GetSecretValueResponseBody getSecretValue(String secretName) {
		GetSecretValueRequestWrapper wrapper = new GetSecretValueRequestWrapper(kmsClient);
		GetSecretValueRequest request = wrapper.builder().build()
				.setSecretName(secretName);
		return wrapper.execute(request).getBody();
	}

	public PutSecretValueResponseBody putSecretValue(String versionId, String secretName, String secretData) {
		PutSecretValueRequestWrapper wrapper = new PutSecretValueRequestWrapper(kmsClient);
		PutSecretValueRequest request = wrapper.builder().build()
				.setVersionId(versionId)
				.setSecretName(secretName)
				.setSecretData(secretData);
		return wrapper.execute(request).getBody();
	}
}

