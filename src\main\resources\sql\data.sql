-- Caidao OAuth2 Client Credentials Service Initial Data

USE caidao_auth;

-- Insert default OAuth2 clients (client credentials only)
INSERT INTO oauth_client_details (
    client_id,
    client_secret,
    scope,
    authorities,
    access_token_validity,
    client_name,
    client_description,
    enabled,
    created_time,
    updated_time
) VALUES
(
    'caidao-api-client',
    '$2a$12$rKZKvYnqKrXa5qU5qU5qUOqKrXa5qU5qU5qUOqKrXa5qU5qU5qUOq', -- caidao-api-secret
    'read,write,admin',
    'ROLE_CLIENT,ROLE_API',
    3600,
    'Caidao API Client',
    'Server-to-server API client for Caidao platform',
    TRUE,
    NOW(),
    NOW()
),
(
    'caidao-service-client',
    '$2a$12$rKZKvYnqKrXa5qU5qU5qUOqKrXa5qU5qU5qUOqKrXa5qU5qU5qUOq', -- caidao-service-secret
    'read,write',
    'ROLE_CLIENT,ROLE_SERVICE',
    3600,
    'Caidao Service Client',
    'Microservice client for inter-service communication',
    TRUE,
    NOW(),
    NOW()
),
(
    'caidao-test-client',
    '$2a$12$rKZKvYnqKrXa5qU5qU5qUOqKrXa5qU5qU5qUOqKrXa5qU5qU5qUOq', -- caidao-test-secret
    'read',
    'ROLE_CLIENT',
    1800,
    'Caidao Test Client',
    'Test client for development and testing purposes',
    TRUE,
    NOW(),
    NOW()
)
ON DUPLICATE KEY UPDATE
    client_secret = VALUES(client_secret),
    scope = VALUES(scope),
    authorities = VALUES(authorities),
    access_token_validity = VALUES(access_token_validity),
    client_name = VALUES(client_name),
    client_description = VALUES(client_description),
    updated_time = NOW();
