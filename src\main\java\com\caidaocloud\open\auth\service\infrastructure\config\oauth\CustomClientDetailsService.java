package com.caidaocloud.open.auth.service.infrastructure.config.oauth;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.oauth2.provider.ClientDetails;
import org.springframework.security.oauth2.provider.ClientDetailsService;
import org.springframework.security.oauth2.provider.ClientRegistrationException;
import org.springframework.security.oauth2.provider.client.BaseClientDetails;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.Collections;

/**
 * Custom Client Details Service for Client Credentials Grant
 * 
 * <AUTHOR>
 */
@Service
@Deprecated
public class CustomClientDetailsService implements ClientDetailsService {

    private static final Logger logger = LoggerFactory.getLogger(CustomClientDetailsService.class);

    @Override
    public ClientDetails loadClientByClientId(String clientId) throws ClientRegistrationException {
        return null;
    }

    // @Autowired
    // private OAuthClientDomainService clientDomainService;

    // @Override
    // public ClientDetails loadClientByClientId(String clientId) throws ClientRegistrationException {
    //     logger.debug("Loading client by client ID: {}", clientId);
    //
    //     OAuthClient client = clientDomainService.getClient(clientId);
    //     if (client == null) {
    //         throw new ClientRegistrationException("Client not found: " + clientId);
    //     }
    //
    //     if (!client.isEnabled()) {
    //         throw new ClientRegistrationException("Client is disabled: " + clientId);
    //     }
    //
    //     BaseClientDetails clientDetails = new BaseClientDetails();
    //     clientDetails.setClientId(client.getClientId());
    //     clientDetails.setClientSecret(client.getClientSecret());
    //
    //     // Only support client_credentials grant type
    //     clientDetails.setAuthorizedGrantTypes(Collections.singletonList("client_credentials"));
    //
    //     // Set scopes
    //     if (StringUtils.hasText(client.getScope())) {
    //         clientDetails.setScope(Arrays.asList(client.getScope().split(",")));
    //     }
    //
    //     // Set authorities
    //     if (StringUtils.hasText(client.getAuthorities())) {
    //         clientDetails.setAuthorities(Arrays.asList(client.getAuthorities().split(","))
    //                 .stream()
    //                 .map(authority -> (org.springframework.security.core.GrantedAuthority) () -> authority)
    //                 .collect(java.util.stream.Collectors.toList()));
    //     }
    //
    //     // Set token validity
    //     if (client.getAccessTokenValidity() != null) {
    //         clientDetails.setAccessTokenValiditySeconds(client.getAccessTokenValidity());
    //     }
    //
    //     // No refresh token for client credentials
    //     clientDetails.setRefreshTokenValiditySeconds(0);
    //
    //     // Auto approve all scopes for client credentials
    //     clientDetails.setAutoApproveScopes(clientDetails.getScope());
    //
    //     logger.debug("Client loaded successfully: {}", clientId);
    //     return clientDetails;
    // }
}
