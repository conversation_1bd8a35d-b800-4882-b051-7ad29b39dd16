package com.caidaocloud.open.auth.service.interfaces.dto.kms;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 获取密钥值请求DTO
 * 
 * <AUTHOR>
 * @date 2025/9/5
 */
@Data
public class GetSecretValueRequestDto {

    /**
     * 密钥名称
     */
    @NotBlank(message = "密钥名称不能为空")
    @Size(min = 1, max = 192, message = "密钥名称长度必须在1-192个字符之间")
    private String secretName;
}
