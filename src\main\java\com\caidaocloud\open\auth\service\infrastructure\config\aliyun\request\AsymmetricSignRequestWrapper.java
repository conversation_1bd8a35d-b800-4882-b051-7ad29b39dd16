package com.caidaocloud.open.auth.service.infrastructure.config.aliyun.request;

import com.aliyun.kms20160120.Client;
import com.aliyun.kms20160120.models.AsymmetricSignRequest;
import com.aliyun.kms20160120.models.AsymmetricSignResponse;
import com.aliyun.tea.NameInMap;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 非对称签名请求包装器
 */
public class AsymmetricSignRequestWrapper extends KmsRequestWrapper<AsymmetricSignRequest, AsymmetricSignResponse> {

    public AsymmetricSignRequestWrapper(Client kmsClient) {
        super(kmsClient, "asymmetricSign");
    }
    //
    // @Override
    // public AsymmetricSignRequestBuilder builder() {
    //     return new AsymmetricSignRequestBuilder(AsymmetricSignRequest.class);
    // }
    //
    // @Setter
    // @Accessors(chain = true)
    // public static class AsymmetricSignRequestBuilder extends RequestBuilder<AsymmetricSignRequest>{
    //
    //
    //     public String algorithm;
    //
    //
    //     public String digest;
    //
    //
    //     public String keyId;
    //
    //
    //     public String keyVersionId;
    //
    //     public AsymmetricSignRequestBuilder(Class<AsymmetricSignRequest> requestClass) {
    //         super(requestClass);
    //     }
    //
    //     @Override
    //     public AsymmetricSignRequest build() {
    //         AsymmetricSignRequest request = new AsymmetricSignRequest();
    //         // TODO: 2025/9/4 校验参数
    //         return request.setAlgorithm(algorithm)
    //                 .setKeyId(keyId)
    //                 .setKeyVersionId(keyVersionId)
    //                 .setDigest(digest);
    //     }
    // }
}