2025-08-30 21:50:48.436 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-08-30 21:50:48.452 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-08-30 21:50:49.168 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$57aba8be] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 21:50:50.314 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-08-30 21:50:50.315 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-08-30 21:50:50.317 [main] INFO  c.c.o.a.service.OpenAuthApplication - No active profile set, falling back to default profiles: default
2025-08-30 21:50:52.364 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-08-30 21:50:52.907 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-30 21:50:52.910 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-30 21:50:52.987 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 51ms. Found 0 repository interfaces.
2025-08-30 21:50:53.199 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-08-30 21:50:53.771 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=d1c4090b-2810-3a5e-b670-eb417c19739b
2025-08-30 21:50:53.843 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IConditionFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 21:50:53.847 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IDictFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 21:50:53.850 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IEntityDataFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 21:50:53.854 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IMetadataAuthFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 21:50:53.856 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IMetadataFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 21:50:53.858 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.transaction.configuration.feign.ITransactionFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 21:50:53.982 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.DistributedLockConfiguration' of type [com.jarvis.cache.autoconfigure.DistributedLockConfiguration$$EnhancerBySpringCGLIB$$a357bfed] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 21:50:54.019 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 21:50:54.029 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration$$EnhancerBySpringCGLIB$$50ab640b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 21:50:54.193 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 21:50:54.304 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 21:50:54.312 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoLoadCacheDistributedLock' of type [com.jarvis.cache.redis.SpringRedisLock] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 21:50:54.328 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoload.cache-com.jarvis.cache.autoconfigure.AutoloadCacheProperties' of type [com.jarvis.cache.autoconfigure.AutoloadCacheProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 21:50:54.329 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure' of type [com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure$$EnhancerBySpringCGLIB$$e9959e4f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 21:50:54.506 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$3b91a5c1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 21:50:54.811 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration$$EnhancerBySpringCGLIB$$70d29f6c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 21:50:54.849 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$$EnhancerBySpringCGLIB$$76d5b3a9] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 21:50:54.874 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheSerializer' of type [com.jarvis.cache.serializer.JdkSerializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 21:50:55.249 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2025-08-30 21:50:55.251 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2025-08-30 21:50:55.907 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheCacheManager' of type [com.jarvis.cache.redis.SpringRedisCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 21:50:55.936 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheScriptParser' of type [com.jarvis.cache.script.SpringELParser] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 21:50:55.965 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheHandler' of type [com.jarvis.cache.CacheHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 21:50:55.981 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteTransactionalInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 21:50:55.992 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 21:50:55.997 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 21:50:55.999 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 21:50:56.003 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheMethodInterceptor' of type [com.jarvis.cache.interceptor.CacheMethodInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 21:50:56.004 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 21:50:56.044 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$57aba8be] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 21:50:56.743 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-08-30 21:50:56.766 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-30 21:50:56.775 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-30 21:50:56.776 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet Engine: Apache Tomcat/9.0.12
2025-08-30 21:50:56.786 [main] INFO  o.a.c.core.AprLifecycleListener - The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\bin\default;C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\bin;C:\windows\system32;C:\windows;C:\windows\system32;C:\windows;C:\windows\System32\Wbem;C:\windows\System32\WindowsPowerShell\v1.0\;C:\windows\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Users\<USER>\.jdks\temurin-1.8.0_302\bin;C:\Users\<USER>\.jdks\temurin-1.8.0_302\jre\bin;C:\Program Files\JetBrains\IntelliJ IDEA 2021.2.2\plugins\maven\lib\maven3\bin;C:\Program Files\Kubernetes\Minikube;;C:\minikube;C:\Program Files\Docker\Docker\resources\bin;C:\ProgramData\DockerDesktop\version-bin;C:\Program Files (x86)\Tencent\微信web开发者工具\dll;C:\Program Files\nodejs\;C:\Program Files\cursor\resources\app\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Users\<USER>\scoop\shims;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\IntelliJ IDEA 2021.2.2\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;.]
2025-08-30 21:50:57.066 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-30 21:50:57.066 [main] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6713 ms
2025-08-30 21:50:59.463 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'characterEncodingFilter' to: [/*]
2025-08-30 21:50:59.463 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'webMvcMetricsFilter' to: [/*]
2025-08-30 21:50:59.463 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2025-08-30 21:50:59.463 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'formContentFilter' to: [/*]
2025-08-30 21:50:59.464 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'requestContextFilter' to: [/*]
2025-08-30 21:50:59.465 [main] INFO  o.s.b.w.s.DelegatingFilterProxyRegistrationBean - Mapping filter: 'springSecurityFilterChain' to: [/*]
2025-08-30 21:50:59.466 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'commonScopeFilter' to urls: [/*]
2025-08-30 21:50:59.467 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'httpTraceFilter' to: [/*]
2025-08-30 21:50:59.467 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'HTTPBasicAuthorizeAttribute' to urls: [/autoload-cache-ui.html, /autoload-cache/*]
2025-08-30 21:50:59.468 [main] INFO  o.s.b.w.s.ServletRegistrationBean - Servlet dispatcherServlet mapped to [/]
2025-08-30 21:51:00.810 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.caidaocloud.open.auth.service.infrastructure.persistence.po.OAuthClientPo".
2025-08-30 21:51:00.818 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.caidaocloud.open.auth.service.infrastructure.persistence.po.OAuthClientPo ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 21:51:01.382 [main] INFO  o.s.s.o.p.t.s.JwtAccessTokenConverter - Configured with RSA signing key
2025-08-30 21:51:01.405 [main] INFO  o.s.s.o.p.t.s.JwtAccessTokenConverter - Signing and verification RSA keys match
2025-08-30 21:51:02.423 [main] INFO  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 04a2a7f7-eff8-4263-a017-afcbb0fd89dc

2025-08-30 21:51:02.712 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Creating filter chain: OrRequestMatcher [requestMatchers=[Ant [pattern='/api/auth/open/v1/oauth/token'], Ant [pattern='/api/auth/open/v1/oauth/token_key'], Ant [pattern='/api/auth/open/v1/oauth/check_token']]], [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3c8fe800, org.springframework.security.web.context.SecurityContextPersistenceFilter@5bf871a2, org.springframework.security.web.header.HeaderWriterFilter@bebc7bce, org.springframework.security.web.authentication.logout.LogoutFilter@1e22afba, org.springframework.security.oauth2.provider.client.ClientCredentialsTokenEndpointFilter@7a8e3226, com.caidaocloud.open.auth.service.infrastructure.config.oauth.OAuthClientCredentialsTokenEndpointFilter@f88d4a03, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@abb21f81, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@23ea598f, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@77c71e24, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@32610cfb, org.springframework.security.web.session.SessionManagementFilter@486ade70, org.springframework.security.web.access.ExceptionTranslationFilter@802006a0, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@51b0b90b]
2025-08-30 21:51:02.723 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1edf96f, org.springframework.security.web.context.SecurityContextPersistenceFilter@ac4a0abc, org.springframework.security.web.header.HeaderWriterFilter@99feee2e, org.springframework.security.web.authentication.logout.LogoutFilter@c6cfa8d7, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@fc635158, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@777b43d3, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@17b1f75f, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@80035033, org.springframework.security.web.session.SessionManagementFilter@c9b8f6c3, org.springframework.security.web.access.ExceptionTranslationFilter@e6f489cc, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@1ac6b114]
2025-08-30 21:51:02.787 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-08-30 21:51:02.787 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-08-30 21:51:02.799 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-08-30 21:51:02.799 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-08-30 21:51:03.354 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2025-08-30 21:51:03.760 [main] INFO  o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService
2025-08-30 21:51:05.902 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-08-30 21:51:06.975 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-30 21:51:06.980 [main] INFO  o.a.tomcat.util.net.NioSelectorPool - Using a shared selector for servlet write/read
2025-08-30 21:51:07.006 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-08-30 21:51:07.070 [main] ERROR c.a.c.n.r.NacosServiceRegistry - nacos registry, caidao-open-auth-service register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='', endpoint='', namespace='', watchDelay=30000, logName='', service='caidao-open-auth-service', weight=1.0, clusterName='DEFAULT', namingLoadCacheAtStart='false', metadata={preserved.register.source=SPRING_CLOUD}, registerEnabled=true, ip='**************', networkInterface='', port=8080, secure=false, accessKey='', secretKey=''}},
com.alibaba.nacos.api.exception.NacosException: no server available
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:521)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:501)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:496)
	at com.alibaba.nacos.client.naming.net.NamingProxy.registerService(NamingProxy.java:249)
	at com.alibaba.nacos.client.naming.NacosNamingService.registerInstance(NacosNamingService.java:218)
	at com.alibaba.nacos.client.naming.NacosNamingService.registerInstance(NacosNamingService.java:207)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:61)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.register(AbstractAutoServiceRegistration.java:219)
	at com.alibaba.cloud.nacos.registry.NacosAutoServiceRegistration.register(NacosAutoServiceRegistration.java:74)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.start(AbstractAutoServiceRegistration.java:118)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.bind(AbstractAutoServiceRegistration.java:82)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.onApplicationEvent(AbstractAutoServiceRegistration.java:69)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.onApplicationEvent(AbstractAutoServiceRegistration.java:32)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:172)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:165)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:139)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:398)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:355)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.finishRefresh(ServletWebServerApplicationContext.java:164)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:549)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:140)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:775)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1260)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1248)
	at com.caidaocloud.open.auth.service.OpenAuthApplication.main(OpenAuthApplication.java:17)
2025-08-30 21:51:07.074 [main] INFO  c.c.o.a.service.OpenAuthApplication - Started OpenAuthApplication in 19.914 seconds (JVM running for 23.727)
2025-08-30 21:51:31.974 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-30 21:51:31.975 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-30 21:51:32.013 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 38 ms
2025-08-30 21:51:42.984 [http-nio-8080-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-30 21:51:43.188 [http-nio-8080-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-30 21:51:43.250 [http-nio-8080-exec-1] WARN  o.s.s.c.bcrypt.BCryptPasswordEncoder - Encoded password does not look like BCrypt
2025-08-30 22:14:04.095 [Thread-26] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-08-30 22:14:04.104 [Thread-32] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-08-30 22:14:04.117 [Thread-32] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-08-30 22:14:04.240 [Thread-26] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-08-30 22:14:05.785 [Thread-37] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-08-30 22:14:05.826 [Thread-37] ERROR c.a.c.n.r.NacosServiceRegistry - ERR_NACOS_DEREGISTER, de-register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='', endpoint='', namespace='', watchDelay=30000, logName='', service='caidao-open-auth-service', weight=1.0, clusterName='DEFAULT', namingLoadCacheAtStart='false', metadata={preserved.register.source=SPRING_CLOUD}, registerEnabled=true, ip='**************', networkInterface='', port=8080, secure=false, accessKey='', secretKey=''}},
com.alibaba.nacos.api.exception.NacosException: no server available
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:521)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:501)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:496)
	at com.alibaba.nacos.client.naming.net.NamingProxy.deregisterService(NamingProxy.java:274)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:258)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:244)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:233)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:85)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.deregister(AbstractAutoServiceRegistration.java:236)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.stop(AbstractAutoServiceRegistration.java:251)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.destroy(AbstractAutoServiceRegistration.java:188)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:363)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:321)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:156)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:240)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:571)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:543)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1052)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:504)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1059)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1035)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1011)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:931)
2025-08-30 22:14:05.828 [Thread-37] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-08-30 22:14:05.842 [Thread-37] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'applicationTaskExecutor'
2025-08-30 22:14:05.938 [Thread-37] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2025-08-30 22:14:07.220 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - RSA_PKCS1_SHA_256 was destroying!
2025-08-30 22:14:07.221 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - your-kms-key-id was destroying!
2025-08-30 22:14:07.221 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - false was destroying!
2025-08-30 22:14:07.221 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - your-key-version-id was destroying!
2025-08-30 22:14:07.226 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - class com.alibaba.nacos.spring.context.annotation.config.NacosValueAnnotationBeanPostProcessor was destroying!
2025-08-30 22:14:07.227 [Thread-37] INFO  c.a.n.s.b.f.a.AnnotationNacosInjectedBeanPostProcessor - class com.alibaba.nacos.spring.beans.factory.annotation.AnnotationNacosInjectedBeanPostProcessor was destroying!
2025-08-30 22:14:07.228 [Thread-37] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-30 22:14:07.239 [Thread-37] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-30 22:14:20.309 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-08-30 22:14:20.316 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-08-30 22:14:21.043 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$600f8789] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:14:22.057 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-08-30 22:14:22.058 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-08-30 22:14:22.061 [main] INFO  c.c.o.a.service.OpenAuthApplication - No active profile set, falling back to default profiles: default
2025-08-30 22:14:23.822 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-08-30 22:14:24.481 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-30 22:14:24.485 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-30 22:14:24.568 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 59ms. Found 0 repository interfaces.
2025-08-30 22:14:24.765 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-08-30 22:14:25.434 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=d1c4090b-2810-3a5e-b670-eb417c19739b
2025-08-30 22:14:25.517 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IConditionFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:14:25.520 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IDictFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:14:25.525 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IEntityDataFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:14:25.530 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IMetadataAuthFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:14:25.534 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IMetadataFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:14:25.537 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.transaction.configuration.feign.ITransactionFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:14:25.660 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.DistributedLockConfiguration' of type [com.jarvis.cache.autoconfigure.DistributedLockConfiguration$$EnhancerBySpringCGLIB$$abbb9eb8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:14:25.703 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:14:25.724 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration$$EnhancerBySpringCGLIB$$590f42d6] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:14:25.928 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:14:26.054 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:14:26.068 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoLoadCacheDistributedLock' of type [com.jarvis.cache.redis.SpringRedisLock] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:14:26.094 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoload.cache-com.jarvis.cache.autoconfigure.AutoloadCacheProperties' of type [com.jarvis.cache.autoconfigure.AutoloadCacheProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:14:26.094 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure' of type [com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure$$EnhancerBySpringCGLIB$$f1f97d1a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:14:26.369 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$43f5848c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:14:26.697 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration$$EnhancerBySpringCGLIB$$79367e37] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:14:26.768 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$$EnhancerBySpringCGLIB$$7f399274] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:14:26.794 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheSerializer' of type [com.jarvis.cache.serializer.JdkSerializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:14:27.265 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2025-08-30 22:14:27.268 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2025-08-30 22:14:28.129 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheCacheManager' of type [com.jarvis.cache.redis.SpringRedisCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:14:28.162 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheScriptParser' of type [com.jarvis.cache.script.SpringELParser] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:14:28.205 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheHandler' of type [com.jarvis.cache.CacheHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:14:28.234 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteTransactionalInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:14:28.248 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:14:28.258 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:14:28.259 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:14:28.266 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheMethodInterceptor' of type [com.jarvis.cache.interceptor.CacheMethodInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:14:28.267 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:14:28.321 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$600f8789] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:14:29.231 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-08-30 22:14:29.255 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-30 22:14:29.267 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-30 22:14:29.268 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet Engine: Apache Tomcat/9.0.12
2025-08-30 22:14:29.281 [main] INFO  o.a.c.core.AprLifecycleListener - The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\bin\default;C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\bin;C:\windows\system32;C:\windows;C:\windows\system32;C:\windows;C:\windows\System32\Wbem;C:\windows\System32\WindowsPowerShell\v1.0\;C:\windows\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Users\<USER>\.jdks\temurin-1.8.0_302\bin;C:\Users\<USER>\.jdks\temurin-1.8.0_302\jre\bin;C:\Program Files\JetBrains\IntelliJ IDEA 2021.2.2\plugins\maven\lib\maven3\bin;C:\Program Files\Kubernetes\Minikube;;C:\minikube;C:\Program Files\Docker\Docker\resources\bin;C:\ProgramData\DockerDesktop\version-bin;C:\Program Files (x86)\Tencent\微信web开发者工具\dll;C:\Program Files\nodejs\;C:\Program Files\cursor\resources\app\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Users\<USER>\scoop\shims;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\IntelliJ IDEA 2021.2.2\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;.]
2025-08-30 22:14:29.664 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-30 22:14:29.664 [main] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 7563 ms
2025-08-30 22:14:33.563 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'characterEncodingFilter' to: [/*]
2025-08-30 22:14:33.565 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'webMvcMetricsFilter' to: [/*]
2025-08-30 22:14:33.566 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2025-08-30 22:14:33.566 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'formContentFilter' to: [/*]
2025-08-30 22:14:33.566 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'requestContextFilter' to: [/*]
2025-08-30 22:14:33.568 [main] INFO  o.s.b.w.s.DelegatingFilterProxyRegistrationBean - Mapping filter: 'springSecurityFilterChain' to: [/*]
2025-08-30 22:14:33.568 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'commonScopeFilter' to urls: [/*]
2025-08-30 22:14:33.569 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'httpTraceFilter' to: [/*]
2025-08-30 22:14:33.569 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'HTTPBasicAuthorizeAttribute' to urls: [/autoload-cache-ui.html, /autoload-cache/*]
2025-08-30 22:14:33.569 [main] INFO  o.s.b.w.s.ServletRegistrationBean - Servlet dispatcherServlet mapped to [/]
2025-08-30 22:14:35.028 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.caidaocloud.open.auth.service.infrastructure.persistence.po.OAuthClientPo".
2025-08-30 22:14:35.039 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.caidaocloud.open.auth.service.infrastructure.persistence.po.OAuthClientPo ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 22:14:35.759 [main] INFO  o.s.s.o.p.t.s.JwtAccessTokenConverter - Configured with RSA signing key
2025-08-30 22:14:35.789 [main] INFO  o.s.s.o.p.t.s.JwtAccessTokenConverter - Signing and verification RSA keys match
2025-08-30 22:14:37.057 [main] INFO  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: a4d1e95f-0a21-4e93-b8c1-fd75630fe2fb

2025-08-30 22:14:37.367 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Creating filter chain: OrRequestMatcher [requestMatchers=[Ant [pattern='/api/auth/open/v1/oauth/token'], Ant [pattern='/api/auth/open/v1/oauth/token_key'], Ant [pattern='/api/auth/open/v1/oauth/check_token']]], [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@263dac9a, org.springframework.security.web.context.SecurityContextPersistenceFilter@64c0c144, org.springframework.security.web.header.HeaderWriterFilter@e67041d8, org.springframework.security.web.authentication.logout.LogoutFilter@105af701, org.springframework.security.oauth2.provider.client.ClientCredentialsTokenEndpointFilter@16f06ce6, com.caidaocloud.open.auth.service.infrastructure.config.oauth.OAuthClientCredentialsTokenEndpointFilter@e223102a, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@dbb57577, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2863c2b0, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@8c8a89ba, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4b3907f4, org.springframework.security.web.session.SessionManagementFilter@275bda07, org.springframework.security.web.access.ExceptionTranslationFilter@ec1305e0, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@98098587]
2025-08-30 22:14:37.376 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@52b0f25b, org.springframework.security.web.context.SecurityContextPersistenceFilter@4be597, org.springframework.security.web.header.HeaderWriterFilter@d0a08e69, org.springframework.security.web.authentication.logout.LogoutFilter@eecd7e56, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@55e057a3, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@82205c55, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@c5cf23d4, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@e6fe9bf4, org.springframework.security.web.session.SessionManagementFilter@ea0624e4, org.springframework.security.web.access.ExceptionTranslationFilter@25bf5df1, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@2ae612ca]
2025-08-30 22:14:37.442 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-08-30 22:14:37.443 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-08-30 22:14:37.450 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-08-30 22:14:37.450 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-08-30 22:14:38.113 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2025-08-30 22:14:38.520 [main] INFO  o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService
2025-08-30 22:14:41.253 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-08-30 22:14:42.521 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-30 22:14:42.523 [main] INFO  o.a.tomcat.util.net.NioSelectorPool - Using a shared selector for servlet write/read
2025-08-30 22:14:42.571 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-08-30 22:14:42.626 [main] ERROR c.a.c.n.r.NacosServiceRegistry - nacos registry, caidao-open-auth-service register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='', endpoint='', namespace='', watchDelay=30000, logName='', service='caidao-open-auth-service', weight=1.0, clusterName='DEFAULT', namingLoadCacheAtStart='false', metadata={preserved.register.source=SPRING_CLOUD}, registerEnabled=true, ip='**************', networkInterface='', port=8080, secure=false, accessKey='', secretKey=''}},
com.alibaba.nacos.api.exception.NacosException: no server available
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:521)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:501)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:496)
	at com.alibaba.nacos.client.naming.net.NamingProxy.registerService(NamingProxy.java:249)
	at com.alibaba.nacos.client.naming.NacosNamingService.registerInstance(NacosNamingService.java:218)
	at com.alibaba.nacos.client.naming.NacosNamingService.registerInstance(NacosNamingService.java:207)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:61)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.register(AbstractAutoServiceRegistration.java:219)
	at com.alibaba.cloud.nacos.registry.NacosAutoServiceRegistration.register(NacosAutoServiceRegistration.java:74)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.start(AbstractAutoServiceRegistration.java:118)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.bind(AbstractAutoServiceRegistration.java:82)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.onApplicationEvent(AbstractAutoServiceRegistration.java:69)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.onApplicationEvent(AbstractAutoServiceRegistration.java:32)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:172)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:165)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:139)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:398)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:355)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.finishRefresh(ServletWebServerApplicationContext.java:164)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:549)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:140)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:775)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1260)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1248)
	at com.caidaocloud.open.auth.service.OpenAuthApplication.main(OpenAuthApplication.java:17)
2025-08-30 22:14:42.632 [main] INFO  c.c.o.a.service.OpenAuthApplication - Started OpenAuthApplication in 23.823 seconds (JVM running for 26.215)
2025-08-30 22:15:05.137 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-30 22:15:05.137 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-30 22:15:05.173 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 36 ms
2025-08-30 22:15:05.454 [http-nio-8080-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-30 22:15:05.672 [http-nio-8080-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-30 22:15:05.952 [http-nio-8080-exec-1] INFO  o.s.s.o.p.endpoint.TokenEndpoint - Handling error: HttpRequestMethodNotSupportedException, Request method 'GET' not supported
2025-08-30 22:19:42.322 [Thread-32] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-08-30 22:19:42.322 [Thread-26] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-08-30 22:19:42.324 [Thread-32] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-08-30 22:19:42.326 [Thread-26] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-08-30 22:19:42.340 [Thread-37] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-08-30 22:19:42.344 [Thread-37] ERROR c.a.c.n.r.NacosServiceRegistry - ERR_NACOS_DEREGISTER, de-register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='', endpoint='', namespace='', watchDelay=30000, logName='', service='caidao-open-auth-service', weight=1.0, clusterName='DEFAULT', namingLoadCacheAtStart='false', metadata={preserved.register.source=SPRING_CLOUD}, registerEnabled=true, ip='**************', networkInterface='', port=8080, secure=false, accessKey='', secretKey=''}},
com.alibaba.nacos.api.exception.NacosException: no server available
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:521)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:501)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:496)
	at com.alibaba.nacos.client.naming.net.NamingProxy.deregisterService(NamingProxy.java:274)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:258)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:244)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:233)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:85)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.deregister(AbstractAutoServiceRegistration.java:236)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.stop(AbstractAutoServiceRegistration.java:251)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.destroy(AbstractAutoServiceRegistration.java:188)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:363)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:321)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:156)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:240)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:571)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:543)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1052)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:504)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1059)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1035)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1011)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:931)
2025-08-30 22:19:42.344 [Thread-37] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-08-30 22:19:42.347 [Thread-37] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'applicationTaskExecutor'
2025-08-30 22:19:42.364 [Thread-37] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2025-08-30 22:19:43.514 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - RSA_PKCS1_SHA_256 was destroying!
2025-08-30 22:19:43.514 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - your-kms-key-id was destroying!
2025-08-30 22:19:43.514 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - false was destroying!
2025-08-30 22:19:43.514 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - your-key-version-id was destroying!
2025-08-30 22:19:43.515 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - class com.alibaba.nacos.spring.context.annotation.config.NacosValueAnnotationBeanPostProcessor was destroying!
2025-08-30 22:19:43.515 [Thread-37] INFO  c.a.n.s.b.f.a.AnnotationNacosInjectedBeanPostProcessor - class com.alibaba.nacos.spring.beans.factory.annotation.AnnotationNacosInjectedBeanPostProcessor was destroying!
2025-08-30 22:19:43.515 [Thread-37] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-30 22:19:43.521 [Thread-37] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-30 22:19:53.939 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-08-30 22:19:53.947 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-08-30 22:19:54.632 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$f5fd606a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:19:55.910 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-08-30 22:19:55.910 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-08-30 22:19:55.911 [main] INFO  c.c.o.a.service.OpenAuthApplication - No active profile set, falling back to default profiles: default
2025-08-30 22:19:57.218 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-08-30 22:19:57.794 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-30 22:19:57.799 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-30 22:19:57.914 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 48ms. Found 0 repository interfaces.
2025-08-30 22:19:58.089 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-08-30 22:19:58.782 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=d1c4090b-2810-3a5e-b670-eb417c19739b
2025-08-30 22:19:58.849 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IConditionFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:19:58.852 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IDictFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:19:58.855 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IEntityDataFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:19:58.858 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IMetadataAuthFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:19:58.861 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IMetadataFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:19:58.863 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.transaction.configuration.feign.ITransactionFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:19:58.956 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.DistributedLockConfiguration' of type [com.jarvis.cache.autoconfigure.DistributedLockConfiguration$$EnhancerBySpringCGLIB$$41a97799] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:19:58.989 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:19:59.003 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration$$EnhancerBySpringCGLIB$$eefd1bb7] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:19:59.176 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:19:59.305 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:19:59.316 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoLoadCacheDistributedLock' of type [com.jarvis.cache.redis.SpringRedisLock] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:19:59.337 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoload.cache-com.jarvis.cache.autoconfigure.AutoloadCacheProperties' of type [com.jarvis.cache.autoconfigure.AutoloadCacheProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:19:59.337 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure' of type [com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure$$EnhancerBySpringCGLIB$$87e755fb] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:19:59.494 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$d9e35d6d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:19:59.818 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration$$EnhancerBySpringCGLIB$$f245718] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:19:59.861 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$$EnhancerBySpringCGLIB$$15276b55] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:19:59.886 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheSerializer' of type [com.jarvis.cache.serializer.JdkSerializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:20:00.169 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2025-08-30 22:20:00.173 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2025-08-30 22:20:00.777 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheCacheManager' of type [com.jarvis.cache.redis.SpringRedisCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:20:00.804 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheScriptParser' of type [com.jarvis.cache.script.SpringELParser] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:20:00.829 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheHandler' of type [com.jarvis.cache.CacheHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:20:00.838 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteTransactionalInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:20:00.843 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:20:00.851 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:20:00.853 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:20:00.856 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheMethodInterceptor' of type [com.jarvis.cache.interceptor.CacheMethodInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:20:00.857 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:20:00.889 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$f5fd606a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:20:01.486 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-08-30 22:20:01.507 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-30 22:20:01.512 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-30 22:20:01.512 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet Engine: Apache Tomcat/9.0.12
2025-08-30 22:20:01.526 [main] INFO  o.a.c.core.AprLifecycleListener - The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\bin\default;C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\bin;C:\windows\system32;C:\windows;C:\windows\system32;C:\windows;C:\windows\System32\Wbem;C:\windows\System32\WindowsPowerShell\v1.0\;C:\windows\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Users\<USER>\.jdks\temurin-1.8.0_302\bin;C:\Users\<USER>\.jdks\temurin-1.8.0_302\jre\bin;C:\Program Files\JetBrains\IntelliJ IDEA 2021.2.2\plugins\maven\lib\maven3\bin;C:\Program Files\Kubernetes\Minikube;;C:\minikube;C:\Program Files\Docker\Docker\resources\bin;C:\ProgramData\DockerDesktop\version-bin;C:\Program Files (x86)\Tencent\微信web开发者工具\dll;C:\Program Files\nodejs\;C:\Program Files\cursor\resources\app\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Users\<USER>\scoop\shims;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\IntelliJ IDEA 2021.2.2\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;.]
2025-08-30 22:20:01.771 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-30 22:20:01.771 [main] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 5841 ms
2025-08-30 22:20:03.868 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'characterEncodingFilter' to: [/*]
2025-08-30 22:20:03.869 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'webMvcMetricsFilter' to: [/*]
2025-08-30 22:20:03.869 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2025-08-30 22:20:03.869 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'formContentFilter' to: [/*]
2025-08-30 22:20:03.869 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'requestContextFilter' to: [/*]
2025-08-30 22:20:03.870 [main] INFO  o.s.b.w.s.DelegatingFilterProxyRegistrationBean - Mapping filter: 'springSecurityFilterChain' to: [/*]
2025-08-30 22:20:03.871 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'commonScopeFilter' to urls: [/*]
2025-08-30 22:20:03.871 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'httpTraceFilter' to: [/*]
2025-08-30 22:20:03.871 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'HTTPBasicAuthorizeAttribute' to urls: [/autoload-cache-ui.html, /autoload-cache/*]
2025-08-30 22:20:03.871 [main] INFO  o.s.b.w.s.ServletRegistrationBean - Servlet dispatcherServlet mapped to [/]
2025-08-30 22:20:05.055 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.caidaocloud.open.auth.service.infrastructure.persistence.po.OAuthClientPo".
2025-08-30 22:20:05.066 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.caidaocloud.open.auth.service.infrastructure.persistence.po.OAuthClientPo ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 22:20:05.446 [main] INFO  o.s.s.o.p.t.s.JwtAccessTokenConverter - Configured with RSA signing key
2025-08-30 22:20:05.464 [main] INFO  o.s.s.o.p.t.s.JwtAccessTokenConverter - Signing and verification RSA keys match
2025-08-30 22:20:06.372 [main] INFO  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 8eae894b-feb6-4793-a3f4-c011b983c433

2025-08-30 22:20:06.594 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Creating filter chain: OrRequestMatcher [requestMatchers=[Ant [pattern='/api/auth/open/v1/oauth/token'], Ant [pattern='/api/auth/open/v1/oauth/token_key'], Ant [pattern='/api/auth/open/v1/oauth/check_token']]], [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@835ece67, org.springframework.security.web.context.SecurityContextPersistenceFilter@bae7ff7, org.springframework.security.web.header.HeaderWriterFilter@f034226, org.springframework.security.web.authentication.logout.LogoutFilter@1e95616f, org.springframework.security.oauth2.provider.client.ClientCredentialsTokenEndpointFilter@1f8df672, com.caidaocloud.open.auth.service.infrastructure.config.oauth.OAuthClientCredentialsTokenEndpointFilter@e22244a6, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@2185cd6d, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@72837062, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@68322b83, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@bb2de060, org.springframework.security.web.session.SessionManagementFilter@4fd2cc41, org.springframework.security.web.access.ExceptionTranslationFilter@43617187, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@ca140947]
2025-08-30 22:20:06.602 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@68696727, org.springframework.security.web.context.SecurityContextPersistenceFilter@ee6fa732, org.springframework.security.web.header.HeaderWriterFilter@a0c32aac, org.springframework.security.web.authentication.logout.LogoutFilter@72c0cfa4, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@3ab6eed, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2452823c, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3e570e2b, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@9352876a, org.springframework.security.web.session.SessionManagementFilter@dc421f49, org.springframework.security.web.access.ExceptionTranslationFilter@2b929ce5, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@2133e6c2]
2025-08-30 22:20:06.656 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-08-30 22:20:06.657 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-08-30 22:20:06.669 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-08-30 22:20:06.669 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-08-30 22:20:07.173 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2025-08-30 22:20:07.449 [main] INFO  o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService
2025-08-30 22:20:08.967 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-08-30 22:20:09.818 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-30 22:20:09.820 [main] INFO  o.a.tomcat.util.net.NioSelectorPool - Using a shared selector for servlet write/read
2025-08-30 22:20:09.838 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-08-30 22:20:09.896 [main] ERROR c.a.c.n.r.NacosServiceRegistry - nacos registry, caidao-open-auth-service register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='', endpoint='', namespace='', watchDelay=30000, logName='', service='caidao-open-auth-service', weight=1.0, clusterName='DEFAULT', namingLoadCacheAtStart='false', metadata={preserved.register.source=SPRING_CLOUD}, registerEnabled=true, ip='**************', networkInterface='', port=8080, secure=false, accessKey='', secretKey=''}},
com.alibaba.nacos.api.exception.NacosException: no server available
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:521)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:501)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:496)
	at com.alibaba.nacos.client.naming.net.NamingProxy.registerService(NamingProxy.java:249)
	at com.alibaba.nacos.client.naming.NacosNamingService.registerInstance(NacosNamingService.java:218)
	at com.alibaba.nacos.client.naming.NacosNamingService.registerInstance(NacosNamingService.java:207)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:61)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.register(AbstractAutoServiceRegistration.java:219)
	at com.alibaba.cloud.nacos.registry.NacosAutoServiceRegistration.register(NacosAutoServiceRegistration.java:74)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.start(AbstractAutoServiceRegistration.java:118)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.bind(AbstractAutoServiceRegistration.java:82)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.onApplicationEvent(AbstractAutoServiceRegistration.java:69)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.onApplicationEvent(AbstractAutoServiceRegistration.java:32)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:172)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:165)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:139)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:398)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:355)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.finishRefresh(ServletWebServerApplicationContext.java:164)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:549)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:140)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:775)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1260)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1248)
	at com.caidaocloud.open.auth.service.OpenAuthApplication.main(OpenAuthApplication.java:17)
2025-08-30 22:20:09.900 [main] INFO  c.c.o.a.service.OpenAuthApplication - Started OpenAuthApplication in 17.177 seconds (JVM running for 19.003)
2025-08-30 22:21:16.434 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-30 22:21:16.435 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-30 22:21:16.573 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 138 ms
2025-08-30 22:21:16.875 [http-nio-8080-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-30 22:21:17.077 [http-nio-8080-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-30 22:21:36.690 [Thread-28] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-08-30 22:21:36.690 [Thread-34] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-08-30 22:21:36.691 [Thread-34] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-08-30 22:21:36.692 [Thread-28] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-08-30 22:21:36.698 [Thread-39] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-08-30 22:21:36.700 [Thread-39] ERROR c.a.c.n.r.NacosServiceRegistry - ERR_NACOS_DEREGISTER, de-register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='', endpoint='', namespace='', watchDelay=30000, logName='', service='caidao-open-auth-service', weight=1.0, clusterName='DEFAULT', namingLoadCacheAtStart='false', metadata={preserved.register.source=SPRING_CLOUD}, registerEnabled=true, ip='**************', networkInterface='', port=8080, secure=false, accessKey='', secretKey=''}},
com.alibaba.nacos.api.exception.NacosException: no server available
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:521)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:501)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:496)
	at com.alibaba.nacos.client.naming.net.NamingProxy.deregisterService(NamingProxy.java:274)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:258)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:244)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:233)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:85)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.deregister(AbstractAutoServiceRegistration.java:236)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.stop(AbstractAutoServiceRegistration.java:251)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.destroy(AbstractAutoServiceRegistration.java:188)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:363)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:321)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:156)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:240)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:571)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:543)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1052)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:504)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1059)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1035)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1011)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:931)
2025-08-30 22:21:36.700 [Thread-39] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-08-30 22:21:36.702 [Thread-39] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'applicationTaskExecutor'
2025-08-30 22:21:36.713 [Thread-39] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2025-08-30 22:21:37.922 [Thread-39] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - RSA_PKCS1_SHA_256 was destroying!
2025-08-30 22:21:37.923 [Thread-39] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - your-kms-key-id was destroying!
2025-08-30 22:21:37.923 [Thread-39] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - false was destroying!
2025-08-30 22:21:37.923 [Thread-39] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - your-key-version-id was destroying!
2025-08-30 22:21:37.923 [Thread-39] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - class com.alibaba.nacos.spring.context.annotation.config.NacosValueAnnotationBeanPostProcessor was destroying!
2025-08-30 22:21:37.923 [Thread-39] INFO  c.a.n.s.b.f.a.AnnotationNacosInjectedBeanPostProcessor - class com.alibaba.nacos.spring.beans.factory.annotation.AnnotationNacosInjectedBeanPostProcessor was destroying!
2025-08-30 22:21:37.924 [Thread-39] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-30 22:21:37.928 [Thread-39] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-30 22:21:43.692 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-08-30 22:21:43.698 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-08-30 22:21:44.353 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$6e328ed8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:21:45.181 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-08-30 22:21:45.182 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-08-30 22:21:45.182 [main] INFO  c.c.o.a.service.OpenAuthApplication - No active profile set, falling back to default profiles: default
2025-08-30 22:21:46.439 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-08-30 22:21:46.951 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-30 22:21:46.954 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-30 22:21:47.026 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 57ms. Found 0 repository interfaces.
2025-08-30 22:21:47.201 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-08-30 22:21:47.770 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=d1c4090b-2810-3a5e-b670-eb417c19739b
2025-08-30 22:21:47.847 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IConditionFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:21:47.851 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IDictFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:21:47.854 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IEntityDataFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:21:47.861 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IMetadataAuthFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:21:47.867 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IMetadataFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:21:47.870 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.transaction.configuration.feign.ITransactionFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:21:47.992 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.DistributedLockConfiguration' of type [com.jarvis.cache.autoconfigure.DistributedLockConfiguration$$EnhancerBySpringCGLIB$$b9dea607] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:21:48.028 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:21:48.042 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration$$EnhancerBySpringCGLIB$$67324a25] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:21:48.195 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:21:48.297 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:21:48.304 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoLoadCacheDistributedLock' of type [com.jarvis.cache.redis.SpringRedisLock] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:21:48.324 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoload.cache-com.jarvis.cache.autoconfigure.AutoloadCacheProperties' of type [com.jarvis.cache.autoconfigure.AutoloadCacheProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:21:48.325 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure' of type [com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure$$EnhancerBySpringCGLIB$$1c8469] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:21:48.513 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$52188bdb] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:21:48.769 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration$$EnhancerBySpringCGLIB$$87598586] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:21:48.806 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$$EnhancerBySpringCGLIB$$8d5c99c3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:21:48.829 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheSerializer' of type [com.jarvis.cache.serializer.JdkSerializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:21:49.157 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2025-08-30 22:21:49.161 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2025-08-30 22:21:49.711 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheCacheManager' of type [com.jarvis.cache.redis.SpringRedisCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:21:49.732 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheScriptParser' of type [com.jarvis.cache.script.SpringELParser] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:21:49.754 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheHandler' of type [com.jarvis.cache.CacheHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:21:49.770 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteTransactionalInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:21:49.782 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:21:49.786 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:21:49.787 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:21:49.791 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheMethodInterceptor' of type [com.jarvis.cache.interceptor.CacheMethodInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:21:49.792 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:21:49.820 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$6e328ed8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:21:50.428 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-08-30 22:21:50.448 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-30 22:21:50.455 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-30 22:21:50.455 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet Engine: Apache Tomcat/9.0.12
2025-08-30 22:21:50.462 [main] INFO  o.a.c.core.AprLifecycleListener - The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\bin\default;C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\bin;C:\windows\system32;C:\windows;C:\windows\system32;C:\windows;C:\windows\System32\Wbem;C:\windows\System32\WindowsPowerShell\v1.0\;C:\windows\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Users\<USER>\.jdks\temurin-1.8.0_302\bin;C:\Users\<USER>\.jdks\temurin-1.8.0_302\jre\bin;C:\Program Files\JetBrains\IntelliJ IDEA 2021.2.2\plugins\maven\lib\maven3\bin;C:\Program Files\Kubernetes\Minikube;;C:\minikube;C:\Program Files\Docker\Docker\resources\bin;C:\ProgramData\DockerDesktop\version-bin;C:\Program Files (x86)\Tencent\微信web开发者工具\dll;C:\Program Files\nodejs\;C:\Program Files\cursor\resources\app\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Users\<USER>\scoop\shims;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\IntelliJ IDEA 2021.2.2\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;.]
2025-08-30 22:21:50.749 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-30 22:21:50.749 [main] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 5544 ms
2025-08-30 22:21:53.260 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'characterEncodingFilter' to: [/*]
2025-08-30 22:21:53.262 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'webMvcMetricsFilter' to: [/*]
2025-08-30 22:21:53.263 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2025-08-30 22:21:53.263 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'formContentFilter' to: [/*]
2025-08-30 22:21:53.264 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'requestContextFilter' to: [/*]
2025-08-30 22:21:53.265 [main] INFO  o.s.b.w.s.DelegatingFilterProxyRegistrationBean - Mapping filter: 'springSecurityFilterChain' to: [/*]
2025-08-30 22:21:53.265 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'commonScopeFilter' to urls: [/*]
2025-08-30 22:21:53.265 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'httpTraceFilter' to: [/*]
2025-08-30 22:21:53.266 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'HTTPBasicAuthorizeAttribute' to urls: [/autoload-cache-ui.html, /autoload-cache/*]
2025-08-30 22:21:53.266 [main] INFO  o.s.b.w.s.ServletRegistrationBean - Servlet dispatcherServlet mapped to [/]
2025-08-30 22:21:54.325 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.caidaocloud.open.auth.service.infrastructure.persistence.po.OAuthClientPo".
2025-08-30 22:21:54.332 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.caidaocloud.open.auth.service.infrastructure.persistence.po.OAuthClientPo ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 22:21:54.935 [main] INFO  o.s.s.o.p.t.s.JwtAccessTokenConverter - Configured with RSA signing key
2025-08-30 22:21:54.948 [main] INFO  o.s.s.o.p.t.s.JwtAccessTokenConverter - Signing and verification RSA keys match
2025-08-30 22:21:55.846 [main] INFO  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 17dd0438-eae6-4126-acfd-406954e7f519

2025-08-30 22:21:56.095 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Creating filter chain: OrRequestMatcher [requestMatchers=[Ant [pattern='/api/auth/open/v1/oauth/token'], Ant [pattern='/api/auth/open/v1/oauth/token_key'], Ant [pattern='/api/auth/open/v1/oauth/check_token']]], [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@b4481a2b, org.springframework.security.web.context.SecurityContextPersistenceFilter@4659314, org.springframework.security.web.header.HeaderWriterFilter@1c442ec8, org.springframework.security.web.authentication.logout.LogoutFilter@5ebf1a7f, org.springframework.security.oauth2.provider.client.ClientCredentialsTokenEndpointFilter@1aef9319, com.caidaocloud.open.auth.service.infrastructure.config.oauth.OAuthClientCredentialsTokenEndpointFilter@d2c12669, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@c0fabeb9, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@9ed78030, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5e472327, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1922e648, org.springframework.security.web.session.SessionManagementFilter@34c54d10, org.springframework.security.web.access.ExceptionTranslationFilter@fe52cf1e, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@1fa5299d]
2025-08-30 22:21:56.112 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@b97ec343, org.springframework.security.web.context.SecurityContextPersistenceFilter@efc86204, org.springframework.security.web.header.HeaderWriterFilter@322e384, org.springframework.security.web.authentication.logout.LogoutFilter@add8b506, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@6db49e32, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@b803f211, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@ed2f450b, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4346975d, org.springframework.security.web.session.SessionManagementFilter@ef11dde0, org.springframework.security.web.access.ExceptionTranslationFilter@d77b6152, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@befe465]
2025-08-30 22:21:56.166 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-08-30 22:21:56.166 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-08-30 22:21:56.176 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-08-30 22:21:56.177 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-08-30 22:21:56.766 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2025-08-30 22:21:57.087 [main] INFO  o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService
2025-08-30 22:21:58.911 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-08-30 22:22:00.189 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-30 22:22:00.191 [main] INFO  o.a.tomcat.util.net.NioSelectorPool - Using a shared selector for servlet write/read
2025-08-30 22:22:00.213 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-08-30 22:22:00.263 [main] ERROR c.a.c.n.r.NacosServiceRegistry - nacos registry, caidao-open-auth-service register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='', endpoint='', namespace='', watchDelay=30000, logName='', service='caidao-open-auth-service', weight=1.0, clusterName='DEFAULT', namingLoadCacheAtStart='false', metadata={preserved.register.source=SPRING_CLOUD}, registerEnabled=true, ip='**************', networkInterface='', port=8080, secure=false, accessKey='', secretKey=''}},
com.alibaba.nacos.api.exception.NacosException: no server available
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:521)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:501)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:496)
	at com.alibaba.nacos.client.naming.net.NamingProxy.registerService(NamingProxy.java:249)
	at com.alibaba.nacos.client.naming.NacosNamingService.registerInstance(NacosNamingService.java:218)
	at com.alibaba.nacos.client.naming.NacosNamingService.registerInstance(NacosNamingService.java:207)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:61)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.register(AbstractAutoServiceRegistration.java:219)
	at com.alibaba.cloud.nacos.registry.NacosAutoServiceRegistration.register(NacosAutoServiceRegistration.java:74)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.start(AbstractAutoServiceRegistration.java:118)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.bind(AbstractAutoServiceRegistration.java:82)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.onApplicationEvent(AbstractAutoServiceRegistration.java:69)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.onApplicationEvent(AbstractAutoServiceRegistration.java:32)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:172)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:165)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:139)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:398)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:355)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.finishRefresh(ServletWebServerApplicationContext.java:164)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:549)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:140)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:775)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1260)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1248)
	at com.caidaocloud.open.auth.service.OpenAuthApplication.main(OpenAuthApplication.java:17)
2025-08-30 22:22:00.267 [main] INFO  c.c.o.a.service.OpenAuthApplication - Started OpenAuthApplication in 17.882 seconds (JVM running for 19.591)
2025-08-30 22:23:51.499 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-30 22:23:51.500 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-30 22:23:51.531 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 31 ms
2025-08-30 22:25:32.841 [http-nio-8080-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-30 22:25:33.036 [http-nio-8080-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-30 22:26:19.297 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=46s147ms18µs800ns).
2025-08-30 22:27:07.316 [Thread-32] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-08-30 22:27:07.316 [Thread-26] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-08-30 22:27:07.317 [Thread-32] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-08-30 22:27:07.319 [Thread-26] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-08-30 22:27:07.328 [Thread-37] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-08-30 22:27:07.336 [Thread-37] ERROR c.a.c.n.r.NacosServiceRegistry - ERR_NACOS_DEREGISTER, de-register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='', endpoint='', namespace='', watchDelay=30000, logName='', service='caidao-open-auth-service', weight=1.0, clusterName='DEFAULT', namingLoadCacheAtStart='false', metadata={preserved.register.source=SPRING_CLOUD}, registerEnabled=true, ip='**************', networkInterface='', port=8080, secure=false, accessKey='', secretKey=''}},
com.alibaba.nacos.api.exception.NacosException: no server available
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:521)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:501)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:496)
	at com.alibaba.nacos.client.naming.net.NamingProxy.deregisterService(NamingProxy.java:274)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:258)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:244)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:233)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:85)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.deregister(AbstractAutoServiceRegistration.java:236)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.stop(AbstractAutoServiceRegistration.java:251)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.destroy(AbstractAutoServiceRegistration.java:188)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:363)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:321)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:156)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:240)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:571)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:543)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1052)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:504)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1059)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1035)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1011)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:931)
2025-08-30 22:27:07.337 [Thread-37] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-08-30 22:27:07.339 [Thread-37] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'applicationTaskExecutor'
2025-08-30 22:27:07.348 [Thread-37] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2025-08-30 22:27:08.505 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - RSA_PKCS1_SHA_256 was destroying!
2025-08-30 22:27:08.505 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - your-kms-key-id was destroying!
2025-08-30 22:27:08.506 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - false was destroying!
2025-08-30 22:27:08.506 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - your-key-version-id was destroying!
2025-08-30 22:27:08.506 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - class com.alibaba.nacos.spring.context.annotation.config.NacosValueAnnotationBeanPostProcessor was destroying!
2025-08-30 22:27:08.507 [Thread-37] INFO  c.a.n.s.b.f.a.AnnotationNacosInjectedBeanPostProcessor - class com.alibaba.nacos.spring.beans.factory.annotation.AnnotationNacosInjectedBeanPostProcessor was destroying!
2025-08-30 22:27:08.508 [Thread-37] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-30 22:27:08.512 [Thread-37] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-30 22:27:13.138 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-08-30 22:27:13.144 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-08-30 22:27:13.754 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$6bd5bf73] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:27:14.615 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-08-30 22:27:14.616 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-08-30 22:27:14.617 [main] INFO  c.c.o.a.service.OpenAuthApplication - No active profile set, falling back to default profiles: default
2025-08-30 22:27:15.915 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-08-30 22:27:16.398 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-30 22:27:16.403 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-30 22:27:16.499 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 76ms. Found 0 repository interfaces.
2025-08-30 22:27:16.669 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-08-30 22:27:17.171 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=d1c4090b-2810-3a5e-b670-eb417c19739b
2025-08-30 22:27:17.242 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IConditionFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:27:17.246 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IDictFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:27:17.248 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IEntityDataFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:27:17.252 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IMetadataAuthFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:27:17.255 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IMetadataFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:27:17.257 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.transaction.configuration.feign.ITransactionFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:27:17.362 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.DistributedLockConfiguration' of type [com.jarvis.cache.autoconfigure.DistributedLockConfiguration$$EnhancerBySpringCGLIB$$b781d6a2] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:27:17.384 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:27:17.394 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration$$EnhancerBySpringCGLIB$$64d57ac0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:27:17.575 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:27:17.674 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:27:17.684 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoLoadCacheDistributedLock' of type [com.jarvis.cache.redis.SpringRedisLock] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:27:17.714 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoload.cache-com.jarvis.cache.autoconfigure.AutoloadCacheProperties' of type [com.jarvis.cache.autoconfigure.AutoloadCacheProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:27:17.715 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure' of type [com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure$$EnhancerBySpringCGLIB$$fdbfb504] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:27:17.933 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$4fbbbc76] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:27:18.190 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration$$EnhancerBySpringCGLIB$$84fcb621] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:27:18.236 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$$EnhancerBySpringCGLIB$$8affca5e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:27:18.263 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheSerializer' of type [com.jarvis.cache.serializer.JdkSerializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:27:18.604 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2025-08-30 22:27:18.606 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2025-08-30 22:27:19.184 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheCacheManager' of type [com.jarvis.cache.redis.SpringRedisCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:27:19.212 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheScriptParser' of type [com.jarvis.cache.script.SpringELParser] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:27:19.240 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheHandler' of type [com.jarvis.cache.CacheHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:27:19.258 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteTransactionalInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:27:19.265 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:27:19.269 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:27:19.270 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:27:19.273 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheMethodInterceptor' of type [com.jarvis.cache.interceptor.CacheMethodInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:27:19.274 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:27:19.308 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$6bd5bf73] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:27:19.946 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-08-30 22:27:19.969 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-30 22:27:19.977 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-30 22:27:19.978 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet Engine: Apache Tomcat/9.0.12
2025-08-30 22:27:19.988 [main] INFO  o.a.c.core.AprLifecycleListener - The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\bin\default;C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\bin;C:\windows\system32;C:\windows;C:\windows\system32;C:\windows;C:\windows\System32\Wbem;C:\windows\System32\WindowsPowerShell\v1.0\;C:\windows\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Users\<USER>\.jdks\temurin-1.8.0_302\bin;C:\Users\<USER>\.jdks\temurin-1.8.0_302\jre\bin;C:\Program Files\JetBrains\IntelliJ IDEA 2021.2.2\plugins\maven\lib\maven3\bin;C:\Program Files\Kubernetes\Minikube;;C:\minikube;C:\Program Files\Docker\Docker\resources\bin;C:\ProgramData\DockerDesktop\version-bin;C:\Program Files (x86)\Tencent\微信web开发者工具\dll;C:\Program Files\nodejs\;C:\Program Files\cursor\resources\app\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Users\<USER>\scoop\shims;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\IntelliJ IDEA 2021.2.2\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;.]
2025-08-30 22:27:20.254 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-30 22:27:20.254 [main] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 5609 ms
2025-08-30 22:27:22.911 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'characterEncodingFilter' to: [/*]
2025-08-30 22:27:22.912 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'webMvcMetricsFilter' to: [/*]
2025-08-30 22:27:22.913 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2025-08-30 22:27:22.913 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'formContentFilter' to: [/*]
2025-08-30 22:27:22.913 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'requestContextFilter' to: [/*]
2025-08-30 22:27:22.914 [main] INFO  o.s.b.w.s.DelegatingFilterProxyRegistrationBean - Mapping filter: 'springSecurityFilterChain' to: [/*]
2025-08-30 22:27:22.914 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'commonScopeFilter' to urls: [/*]
2025-08-30 22:27:22.914 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'httpTraceFilter' to: [/*]
2025-08-30 22:27:22.915 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'HTTPBasicAuthorizeAttribute' to urls: [/autoload-cache-ui.html, /autoload-cache/*]
2025-08-30 22:27:22.915 [main] INFO  o.s.b.w.s.ServletRegistrationBean - Servlet dispatcherServlet mapped to [/]
2025-08-30 22:27:23.959 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.caidaocloud.open.auth.service.infrastructure.persistence.po.OAuthClientPo".
2025-08-30 22:27:23.971 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.caidaocloud.open.auth.service.infrastructure.persistence.po.OAuthClientPo ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 22:27:24.418 [main] INFO  o.s.s.o.p.t.s.JwtAccessTokenConverter - Configured with RSA signing key
2025-08-30 22:27:24.432 [main] INFO  o.s.s.o.p.t.s.JwtAccessTokenConverter - Signing and verification RSA keys match
2025-08-30 22:27:25.333 [main] INFO  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: ec28e415-8297-43e8-af22-9d0d78d056ee

2025-08-30 22:27:25.580 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Creating filter chain: OrRequestMatcher [requestMatchers=[Ant [pattern='/api/auth/open/v1/oauth/token'], Ant [pattern='/api/auth/open/v1/oauth/token_key'], Ant [pattern='/api/auth/open/v1/oauth/check_token']]], [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@77dfa271, org.springframework.security.web.context.SecurityContextPersistenceFilter@97f1ab5, org.springframework.security.web.header.HeaderWriterFilter@988a172, org.springframework.security.web.authentication.logout.LogoutFilter@71f2fdef, org.springframework.security.oauth2.provider.client.ClientCredentialsTokenEndpointFilter@bf8ee8a9, com.caidaocloud.open.auth.service.infrastructure.config.oauth.OAuthClientCredentialsTokenEndpointFilter@48f8871a, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@30450cad, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@74ad2d35, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@385a85ad, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1e764f41, org.springframework.security.web.session.SessionManagementFilter@927bc82d, org.springframework.security.web.access.ExceptionTranslationFilter@6bdc997, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@b5c83187]
2025-08-30 22:27:25.587 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@6dcd65a1, org.springframework.security.web.context.SecurityContextPersistenceFilter@595e1b14, org.springframework.security.web.header.HeaderWriterFilter@99dc6576, org.springframework.security.web.authentication.logout.LogoutFilter@e74f119e, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@dc228fc9, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@a2aa585f, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@154ab2f6, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5ec137b6, org.springframework.security.web.session.SessionManagementFilter@11700cfd, org.springframework.security.web.access.ExceptionTranslationFilter@1d8c04cd, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6375df4a]
2025-08-30 22:27:25.653 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-08-30 22:27:25.653 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-08-30 22:27:25.662 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-08-30 22:27:25.662 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-08-30 22:27:26.219 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2025-08-30 22:27:26.542 [main] INFO  o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService
2025-08-30 22:27:28.304 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-08-30 22:27:29.303 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-30 22:27:29.307 [main] INFO  o.a.tomcat.util.net.NioSelectorPool - Using a shared selector for servlet write/read
2025-08-30 22:27:29.334 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-08-30 22:27:29.391 [main] ERROR c.a.c.n.r.NacosServiceRegistry - nacos registry, caidao-open-auth-service register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='', endpoint='', namespace='', watchDelay=30000, logName='', service='caidao-open-auth-service', weight=1.0, clusterName='DEFAULT', namingLoadCacheAtStart='false', metadata={preserved.register.source=SPRING_CLOUD}, registerEnabled=true, ip='**************', networkInterface='', port=8080, secure=false, accessKey='', secretKey=''}},
com.alibaba.nacos.api.exception.NacosException: no server available
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:521)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:501)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:496)
	at com.alibaba.nacos.client.naming.net.NamingProxy.registerService(NamingProxy.java:249)
	at com.alibaba.nacos.client.naming.NacosNamingService.registerInstance(NacosNamingService.java:218)
	at com.alibaba.nacos.client.naming.NacosNamingService.registerInstance(NacosNamingService.java:207)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:61)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.register(AbstractAutoServiceRegistration.java:219)
	at com.alibaba.cloud.nacos.registry.NacosAutoServiceRegistration.register(NacosAutoServiceRegistration.java:74)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.start(AbstractAutoServiceRegistration.java:118)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.bind(AbstractAutoServiceRegistration.java:82)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.onApplicationEvent(AbstractAutoServiceRegistration.java:69)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.onApplicationEvent(AbstractAutoServiceRegistration.java:32)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:172)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:165)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:139)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:398)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:355)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.finishRefresh(ServletWebServerApplicationContext.java:164)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:549)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:140)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:775)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1260)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1248)
	at com.caidaocloud.open.auth.service.OpenAuthApplication.main(OpenAuthApplication.java:17)
2025-08-30 22:27:29.394 [main] INFO  c.c.o.a.service.OpenAuthApplication - Started OpenAuthApplication in 17.389 seconds (JVM running for 18.867)
2025-08-30 22:28:21.149 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-30 22:28:21.149 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-30 22:28:21.172 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 23 ms
2025-08-30 22:28:26.004 [http-nio-8080-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-30 22:28:26.171 [http-nio-8080-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-30 22:29:27.227 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m939ms933µs900ns).
2025-08-30 22:30:25.099 [Thread-32] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-08-30 22:30:25.100 [Thread-32] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-08-30 22:30:25.100 [Thread-26] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-08-30 22:30:25.102 [Thread-26] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-08-30 22:30:25.114 [Thread-37] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-08-30 22:30:25.116 [Thread-37] ERROR c.a.c.n.r.NacosServiceRegistry - ERR_NACOS_DEREGISTER, de-register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='', endpoint='', namespace='', watchDelay=30000, logName='', service='caidao-open-auth-service', weight=1.0, clusterName='DEFAULT', namingLoadCacheAtStart='false', metadata={preserved.register.source=SPRING_CLOUD}, registerEnabled=true, ip='**************', networkInterface='', port=8080, secure=false, accessKey='', secretKey=''}},
com.alibaba.nacos.api.exception.NacosException: no server available
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:521)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:501)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:496)
	at com.alibaba.nacos.client.naming.net.NamingProxy.deregisterService(NamingProxy.java:274)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:258)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:244)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:233)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:85)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.deregister(AbstractAutoServiceRegistration.java:236)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.stop(AbstractAutoServiceRegistration.java:251)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.destroy(AbstractAutoServiceRegistration.java:188)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:363)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:321)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:156)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:240)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:571)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:543)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1052)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:504)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1059)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1035)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1011)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:931)
2025-08-30 22:30:25.116 [Thread-37] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-08-30 22:30:25.121 [Thread-37] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'applicationTaskExecutor'
2025-08-30 22:30:25.140 [Thread-37] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2025-08-30 22:30:26.289 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - RSA_PKCS1_SHA_256 was destroying!
2025-08-30 22:30:26.290 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - your-kms-key-id was destroying!
2025-08-30 22:30:26.290 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - false was destroying!
2025-08-30 22:30:26.290 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - your-key-version-id was destroying!
2025-08-30 22:30:26.290 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - class com.alibaba.nacos.spring.context.annotation.config.NacosValueAnnotationBeanPostProcessor was destroying!
2025-08-30 22:30:26.290 [Thread-37] INFO  c.a.n.s.b.f.a.AnnotationNacosInjectedBeanPostProcessor - class com.alibaba.nacos.spring.beans.factory.annotation.AnnotationNacosInjectedBeanPostProcessor was destroying!
2025-08-30 22:30:26.291 [Thread-37] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-30 22:30:26.293 [Thread-37] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-30 22:30:30.938 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-08-30 22:30:30.942 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-08-30 22:30:31.445 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$d46792d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:30:32.189 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-08-30 22:30:32.192 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-08-30 22:30:32.193 [main] INFO  c.c.o.a.service.OpenAuthApplication - No active profile set, falling back to default profiles: default
2025-08-30 22:30:33.497 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-08-30 22:30:34.053 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-30 22:30:34.056 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-30 22:30:34.123 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 51ms. Found 0 repository interfaces.
2025-08-30 22:30:34.309 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-08-30 22:30:34.866 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=d1c4090b-2810-3a5e-b670-eb417c19739b
2025-08-30 22:30:34.939 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IConditionFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:30:34.943 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IDictFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:30:34.945 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IEntityDataFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:30:34.948 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IMetadataAuthFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:30:34.950 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IMetadataFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:30:34.953 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.transaction.configuration.feign.ITransactionFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:30:35.073 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.DistributedLockConfiguration' of type [com.jarvis.cache.autoconfigure.DistributedLockConfiguration$$EnhancerBySpringCGLIB$$58f2905c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:30:35.098 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:30:35.114 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration$$EnhancerBySpringCGLIB$$646347a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:30:35.270 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:30:35.371 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:30:35.381 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoLoadCacheDistributedLock' of type [com.jarvis.cache.redis.SpringRedisLock] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:30:35.402 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoload.cache-com.jarvis.cache.autoconfigure.AutoloadCacheProperties' of type [com.jarvis.cache.autoconfigure.AutoloadCacheProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:30:35.403 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure' of type [com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure$$EnhancerBySpringCGLIB$$9f306ebe] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:30:35.601 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$f12c7630] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:30:35.887 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration$$EnhancerBySpringCGLIB$$266d6fdb] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:30:35.933 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$$EnhancerBySpringCGLIB$$2c708418] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:30:35.967 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheSerializer' of type [com.jarvis.cache.serializer.JdkSerializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:30:36.331 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2025-08-30 22:30:36.333 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2025-08-30 22:30:36.929 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheCacheManager' of type [com.jarvis.cache.redis.SpringRedisCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:30:36.952 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheScriptParser' of type [com.jarvis.cache.script.SpringELParser] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:30:36.975 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheHandler' of type [com.jarvis.cache.CacheHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:30:36.988 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteTransactionalInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:30:37.002 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:30:37.008 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:30:37.008 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:30:37.012 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheMethodInterceptor' of type [com.jarvis.cache.interceptor.CacheMethodInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:30:37.013 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:30:37.038 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$d46792d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:30:37.671 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-08-30 22:30:37.698 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-30 22:30:37.704 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-30 22:30:37.705 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet Engine: Apache Tomcat/9.0.12
2025-08-30 22:30:37.711 [main] INFO  o.a.c.core.AprLifecycleListener - The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\bin\default;C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\bin;C:\windows\system32;C:\windows;C:\windows\system32;C:\windows;C:\windows\System32\Wbem;C:\windows\System32\WindowsPowerShell\v1.0\;C:\windows\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Users\<USER>\.jdks\temurin-1.8.0_302\bin;C:\Users\<USER>\.jdks\temurin-1.8.0_302\jre\bin;C:\Program Files\JetBrains\IntelliJ IDEA 2021.2.2\plugins\maven\lib\maven3\bin;C:\Program Files\Kubernetes\Minikube;;C:\minikube;C:\Program Files\Docker\Docker\resources\bin;C:\ProgramData\DockerDesktop\version-bin;C:\Program Files (x86)\Tencent\微信web开发者工具\dll;C:\Program Files\nodejs\;C:\Program Files\cursor\resources\app\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Users\<USER>\scoop\shims;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\IntelliJ IDEA 2021.2.2\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;.]
2025-08-30 22:30:37.961 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-30 22:30:37.961 [main] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 5734 ms
2025-08-30 22:30:40.543 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'characterEncodingFilter' to: [/*]
2025-08-30 22:30:40.543 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'webMvcMetricsFilter' to: [/*]
2025-08-30 22:30:40.544 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2025-08-30 22:30:40.544 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'formContentFilter' to: [/*]
2025-08-30 22:30:40.544 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'requestContextFilter' to: [/*]
2025-08-30 22:30:40.545 [main] INFO  o.s.b.w.s.DelegatingFilterProxyRegistrationBean - Mapping filter: 'springSecurityFilterChain' to: [/*]
2025-08-30 22:30:40.545 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'commonScopeFilter' to urls: [/*]
2025-08-30 22:30:40.545 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'httpTraceFilter' to: [/*]
2025-08-30 22:30:40.545 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'HTTPBasicAuthorizeAttribute' to urls: [/autoload-cache-ui.html, /autoload-cache/*]
2025-08-30 22:30:40.546 [main] INFO  o.s.b.w.s.ServletRegistrationBean - Servlet dispatcherServlet mapped to [/]
2025-08-30 22:30:41.589 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.caidaocloud.open.auth.service.infrastructure.persistence.po.OAuthClientPo".
2025-08-30 22:30:41.599 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.caidaocloud.open.auth.service.infrastructure.persistence.po.OAuthClientPo ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 22:30:42.005 [main] INFO  o.s.s.o.p.t.s.JwtAccessTokenConverter - Configured with RSA signing key
2025-08-30 22:30:42.014 [main] INFO  o.s.s.o.p.t.s.JwtAccessTokenConverter - Signing and verification RSA keys match
2025-08-30 22:30:42.893 [main] INFO  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: fbdc63d4-7986-4836-aed2-d44254edf32d

2025-08-30 22:30:43.133 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Creating filter chain: OrRequestMatcher [requestMatchers=[Ant [pattern='/api/auth/open/v1/oauth/token'], Ant [pattern='/api/auth/open/v1/oauth/token_key'], Ant [pattern='/api/auth/open/v1/oauth/check_token']]], [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@18f77897, org.springframework.security.web.context.SecurityContextPersistenceFilter@4121ecce, org.springframework.security.web.header.HeaderWriterFilter@e5b337ae, org.springframework.security.web.authentication.logout.LogoutFilter@51bb3b6f, org.springframework.security.oauth2.provider.client.ClientCredentialsTokenEndpointFilter@550cfb69, com.caidaocloud.open.auth.service.infrastructure.config.oauth.OAuthClientCredentialsTokenEndpointFilter@fa34e7cc, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@7e9ae125, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6664bf43, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4690cb56, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2ac19778, org.springframework.security.web.session.SessionManagementFilter@646b4151, org.springframework.security.web.access.ExceptionTranslationFilter@76465962, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@f909c02e]
2025-08-30 22:30:43.140 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@86cc015c, org.springframework.security.web.context.SecurityContextPersistenceFilter@e82675f5, org.springframework.security.web.header.HeaderWriterFilter@e42f39e3, org.springframework.security.web.authentication.logout.LogoutFilter@9ebd84c4, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@b2af1ee0, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7fdd72ab, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@a16f5c44, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@d3b7ed38, org.springframework.security.web.session.SessionManagementFilter@297b6100, org.springframework.security.web.access.ExceptionTranslationFilter@93b0d265, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@8b709a02]
2025-08-30 22:30:43.210 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-08-30 22:30:43.211 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-08-30 22:30:43.217 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-08-30 22:30:43.217 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-08-30 22:30:43.778 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2025-08-30 22:30:44.097 [main] INFO  o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService
2025-08-30 22:30:46.154 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-08-30 22:30:47.162 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-30 22:30:47.163 [main] INFO  o.a.tomcat.util.net.NioSelectorPool - Using a shared selector for servlet write/read
2025-08-30 22:30:47.196 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-08-30 22:30:47.278 [main] ERROR c.a.c.n.r.NacosServiceRegistry - nacos registry, caidao-open-auth-service register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='', endpoint='', namespace='', watchDelay=30000, logName='', service='caidao-open-auth-service', weight=1.0, clusterName='DEFAULT', namingLoadCacheAtStart='false', metadata={preserved.register.source=SPRING_CLOUD}, registerEnabled=true, ip='**************', networkInterface='', port=8080, secure=false, accessKey='', secretKey=''}},
com.alibaba.nacos.api.exception.NacosException: no server available
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:521)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:501)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:496)
	at com.alibaba.nacos.client.naming.net.NamingProxy.registerService(NamingProxy.java:249)
	at com.alibaba.nacos.client.naming.NacosNamingService.registerInstance(NacosNamingService.java:218)
	at com.alibaba.nacos.client.naming.NacosNamingService.registerInstance(NacosNamingService.java:207)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:61)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.register(AbstractAutoServiceRegistration.java:219)
	at com.alibaba.cloud.nacos.registry.NacosAutoServiceRegistration.register(NacosAutoServiceRegistration.java:74)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.start(AbstractAutoServiceRegistration.java:118)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.bind(AbstractAutoServiceRegistration.java:82)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.onApplicationEvent(AbstractAutoServiceRegistration.java:69)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.onApplicationEvent(AbstractAutoServiceRegistration.java:32)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:172)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:165)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:139)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:398)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:355)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.finishRefresh(ServletWebServerApplicationContext.java:164)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:549)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:140)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:775)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1260)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1248)
	at com.caidaocloud.open.auth.service.OpenAuthApplication.main(OpenAuthApplication.java:17)
2025-08-30 22:30:47.282 [main] INFO  c.c.o.a.service.OpenAuthApplication - Started OpenAuthApplication in 17.249 seconds (JVM running for 19.143)
2025-08-30 22:31:58.662 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-30 22:31:58.662 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-30 22:31:58.709 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 47 ms
2025-08-30 22:32:05.228 [http-nio-8080-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-30 22:32:05.412 [http-nio-8080-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-30 22:33:21.484 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=45s805ms199µs800ns).
2025-08-30 22:34:09.194 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=47s710ms162µs300ns).
2025-08-30 22:47:41.840 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=13m32s646ms52µs300ns).
2025-08-30 22:47:42.067 [Thread-32] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-08-30 22:47:42.068 [Thread-26] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-08-30 22:47:42.069 [Thread-32] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-08-30 22:47:42.072 [Thread-26] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-08-30 22:47:42.123 [Thread-37] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-08-30 22:47:42.205 [Thread-37] ERROR c.a.c.n.r.NacosServiceRegistry - ERR_NACOS_DEREGISTER, de-register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='', endpoint='', namespace='', watchDelay=30000, logName='', service='caidao-open-auth-service', weight=1.0, clusterName='DEFAULT', namingLoadCacheAtStart='false', metadata={preserved.register.source=SPRING_CLOUD}, registerEnabled=true, ip='**************', networkInterface='', port=8080, secure=false, accessKey='', secretKey=''}},
com.alibaba.nacos.api.exception.NacosException: no server available
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:521)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:501)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:496)
	at com.alibaba.nacos.client.naming.net.NamingProxy.deregisterService(NamingProxy.java:274)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:258)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:244)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:233)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:85)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.deregister(AbstractAutoServiceRegistration.java:236)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.stop(AbstractAutoServiceRegistration.java:251)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.destroy(AbstractAutoServiceRegistration.java:188)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:363)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:321)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:156)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:240)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:571)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:543)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1052)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:504)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1059)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1035)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1011)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:931)
2025-08-30 22:47:42.206 [Thread-37] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-08-30 22:47:42.208 [Thread-37] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'applicationTaskExecutor'
2025-08-30 22:47:42.228 [Thread-37] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2025-08-30 22:47:43.421 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - RSA_PKCS1_SHA_256 was destroying!
2025-08-30 22:47:43.421 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - your-kms-key-id was destroying!
2025-08-30 22:47:43.422 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - false was destroying!
2025-08-30 22:47:43.422 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - your-key-version-id was destroying!
2025-08-30 22:47:43.423 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - class com.alibaba.nacos.spring.context.annotation.config.NacosValueAnnotationBeanPostProcessor was destroying!
2025-08-30 22:47:43.425 [Thread-37] INFO  c.a.n.s.b.f.a.AnnotationNacosInjectedBeanPostProcessor - class com.alibaba.nacos.spring.beans.factory.annotation.AnnotationNacosInjectedBeanPostProcessor was destroying!
2025-08-30 22:47:43.425 [Thread-37] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-30 22:47:43.429 [Thread-37] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-30 22:54:56.325 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-08-30 22:54:56.331 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-08-30 22:54:56.903 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$b6608d9b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:54:57.775 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-08-30 22:54:57.776 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-08-30 22:54:57.777 [main] INFO  c.c.o.a.service.OpenAuthApplication - No active profile set, falling back to default profiles: default
2025-08-30 22:54:59.075 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-08-30 22:54:59.595 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-30 22:54:59.598 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-30 22:54:59.666 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 52ms. Found 0 repository interfaces.
2025-08-30 22:54:59.823 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-08-30 22:55:00.354 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=d1c4090b-2810-3a5e-b670-eb417c19739b
2025-08-30 22:55:00.423 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IConditionFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:55:00.427 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IDictFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:55:00.430 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IEntityDataFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:55:00.434 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IMetadataAuthFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:55:00.437 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IMetadataFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:55:00.440 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.transaction.configuration.feign.ITransactionFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:55:00.543 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.DistributedLockConfiguration' of type [com.jarvis.cache.autoconfigure.DistributedLockConfiguration$$EnhancerBySpringCGLIB$$20ca4ca] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:55:00.565 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:55:00.574 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration$$EnhancerBySpringCGLIB$$af6048e8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:55:00.724 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:55:00.816 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:55:00.824 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoLoadCacheDistributedLock' of type [com.jarvis.cache.redis.SpringRedisLock] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:55:00.844 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoload.cache-com.jarvis.cache.autoconfigure.AutoloadCacheProperties' of type [com.jarvis.cache.autoconfigure.AutoloadCacheProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:55:00.844 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure' of type [com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure$$EnhancerBySpringCGLIB$$484a832c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:55:01.023 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$9a468a9e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:55:01.298 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration$$EnhancerBySpringCGLIB$$cf878449] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:55:01.332 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$$EnhancerBySpringCGLIB$$d58a9886] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:55:01.360 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheSerializer' of type [com.jarvis.cache.serializer.JdkSerializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:55:01.675 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2025-08-30 22:55:01.677 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2025-08-30 22:55:02.244 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheCacheManager' of type [com.jarvis.cache.redis.SpringRedisCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:55:02.266 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheScriptParser' of type [com.jarvis.cache.script.SpringELParser] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:55:02.296 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheHandler' of type [com.jarvis.cache.CacheHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:55:02.306 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteTransactionalInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:55:02.316 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:55:02.319 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:55:02.320 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:55:02.323 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheMethodInterceptor' of type [com.jarvis.cache.interceptor.CacheMethodInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:55:02.325 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:55:02.352 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$b6608d9b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:55:02.984 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-08-30 22:55:03.004 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-30 22:55:03.011 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-30 22:55:03.012 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet Engine: Apache Tomcat/9.0.12
2025-08-30 22:55:03.020 [main] INFO  o.a.c.core.AprLifecycleListener - The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\bin\default;C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\bin;C:\windows\system32;C:\windows;C:\windows\system32;C:\windows;C:\windows\System32\Wbem;C:\windows\System32\WindowsPowerShell\v1.0\;C:\windows\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Users\<USER>\.jdks\temurin-1.8.0_302\bin;C:\Users\<USER>\.jdks\temurin-1.8.0_302\jre\bin;C:\Program Files\JetBrains\IntelliJ IDEA 2021.2.2\plugins\maven\lib\maven3\bin;C:\Program Files\Kubernetes\Minikube;;C:\minikube;C:\Program Files\Docker\Docker\resources\bin;C:\ProgramData\DockerDesktop\version-bin;C:\Program Files (x86)\Tencent\微信web开发者工具\dll;C:\Program Files\nodejs\;C:\Program Files\cursor\resources\app\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Users\<USER>\scoop\shims;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\IntelliJ IDEA 2021.2.2\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;.]
2025-08-30 22:55:03.286 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-30 22:55:03.286 [main] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 5488 ms
2025-08-30 22:55:05.960 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'characterEncodingFilter' to: [/*]
2025-08-30 22:55:05.960 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'webMvcMetricsFilter' to: [/*]
2025-08-30 22:55:05.961 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2025-08-30 22:55:05.961 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'formContentFilter' to: [/*]
2025-08-30 22:55:05.961 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'requestContextFilter' to: [/*]
2025-08-30 22:55:05.962 [main] INFO  o.s.b.w.s.DelegatingFilterProxyRegistrationBean - Mapping filter: 'springSecurityFilterChain' to: [/*]
2025-08-30 22:55:05.962 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'commonScopeFilter' to urls: [/*]
2025-08-30 22:55:05.962 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'httpTraceFilter' to: [/*]
2025-08-30 22:55:05.962 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'HTTPBasicAuthorizeAttribute' to urls: [/autoload-cache-ui.html, /autoload-cache/*]
2025-08-30 22:55:05.962 [main] INFO  o.s.b.w.s.ServletRegistrationBean - Servlet dispatcherServlet mapped to [/]
2025-08-30 22:55:07.033 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.caidaocloud.open.auth.service.infrastructure.persistence.po.OAuthClientPo".
2025-08-30 22:55:07.046 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.caidaocloud.open.auth.service.infrastructure.persistence.po.OAuthClientPo ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 22:55:07.625 [main] INFO  o.s.s.o.p.t.s.JwtAccessTokenConverter - Configured with RSA signing key
2025-08-30 22:55:07.658 [main] INFO  o.s.s.o.p.t.s.JwtAccessTokenConverter - Signing and verification RSA keys match
2025-08-30 22:55:08.554 [main] INFO  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: ce41cb52-076c-41b9-91cd-4af031145139

2025-08-30 22:55:08.822 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Creating filter chain: OrRequestMatcher [requestMatchers=[Ant [pattern='/api/auth/open/v1/oauth/token'], Ant [pattern='/api/auth/open/v1/oauth/token_key'], Ant [pattern='/api/auth/open/v1/oauth/check_token']]], [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3b9ba6d6, org.springframework.security.web.context.SecurityContextPersistenceFilter@4507dec6, org.springframework.security.web.header.HeaderWriterFilter@c6e54110, org.springframework.security.web.authentication.logout.LogoutFilter@d26dd14, org.springframework.security.oauth2.provider.client.ClientCredentialsTokenEndpointFilter@d9747070, com.caidaocloud.open.auth.service.infrastructure.config.oauth.OAuthClientCredentialsTokenEndpointFilter@e576a35c, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@edb5e58b, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5e8cd74, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@eb67dd55, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7de3ac40, org.springframework.security.web.session.SessionManagementFilter@e822be43, org.springframework.security.web.access.ExceptionTranslationFilter@bd0c1d83, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@a092512e]
2025-08-30 22:55:08.828 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@fa03fde0, org.springframework.security.web.context.SecurityContextPersistenceFilter@cee02d29, org.springframework.security.web.header.HeaderWriterFilter@190840af, org.springframework.security.web.authentication.logout.LogoutFilter@393fd7c7, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@3e7a74c1, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@b886e040, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@bb6a5ae4, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@93b58117, org.springframework.security.web.session.SessionManagementFilter@95dc37e2, org.springframework.security.web.access.ExceptionTranslationFilter@1537e952, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@d9831212]
2025-08-30 22:55:08.894 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-08-30 22:55:08.894 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-08-30 22:55:08.905 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-08-30 22:55:08.906 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-08-30 22:55:09.444 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2025-08-30 22:55:09.818 [main] INFO  o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService
2025-08-30 22:55:11.681 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-08-30 22:55:12.662 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-30 22:55:12.665 [main] INFO  o.a.tomcat.util.net.NioSelectorPool - Using a shared selector for servlet write/read
2025-08-30 22:55:12.699 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-08-30 22:55:12.774 [main] ERROR c.a.c.n.r.NacosServiceRegistry - nacos registry, caidao-open-auth-service register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='', endpoint='', namespace='', watchDelay=30000, logName='', service='caidao-open-auth-service', weight=1.0, clusterName='DEFAULT', namingLoadCacheAtStart='false', metadata={preserved.register.source=SPRING_CLOUD}, registerEnabled=true, ip='**************', networkInterface='', port=8080, secure=false, accessKey='', secretKey=''}},
com.alibaba.nacos.api.exception.NacosException: no server available
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:521)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:501)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:496)
	at com.alibaba.nacos.client.naming.net.NamingProxy.registerService(NamingProxy.java:249)
	at com.alibaba.nacos.client.naming.NacosNamingService.registerInstance(NacosNamingService.java:218)
	at com.alibaba.nacos.client.naming.NacosNamingService.registerInstance(NacosNamingService.java:207)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:61)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.register(AbstractAutoServiceRegistration.java:219)
	at com.alibaba.cloud.nacos.registry.NacosAutoServiceRegistration.register(NacosAutoServiceRegistration.java:74)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.start(AbstractAutoServiceRegistration.java:118)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.bind(AbstractAutoServiceRegistration.java:82)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.onApplicationEvent(AbstractAutoServiceRegistration.java:69)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.onApplicationEvent(AbstractAutoServiceRegistration.java:32)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:172)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:165)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:139)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:398)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:355)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.finishRefresh(ServletWebServerApplicationContext.java:164)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:549)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:140)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:775)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1260)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1248)
	at com.caidaocloud.open.auth.service.OpenAuthApplication.main(OpenAuthApplication.java:17)
2025-08-30 22:55:12.778 [main] INFO  c.c.o.a.service.OpenAuthApplication - Started OpenAuthApplication in 17.689 seconds (JVM running for 19.767)
2025-08-30 22:55:55.389 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-30 22:55:55.390 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-30 22:55:55.428 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 37 ms
2025-08-30 22:55:55.627 [http-nio-8080-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-30 22:55:55.802 [http-nio-8080-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-30 22:57:22.572 [Thread-32] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-08-30 22:57:22.572 [Thread-26] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-08-30 22:57:22.573 [Thread-32] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-08-30 22:57:22.574 [Thread-26] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-08-30 22:57:22.578 [Thread-37] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-08-30 22:57:22.579 [Thread-37] ERROR c.a.c.n.r.NacosServiceRegistry - ERR_NACOS_DEREGISTER, de-register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='', endpoint='', namespace='', watchDelay=30000, logName='', service='caidao-open-auth-service', weight=1.0, clusterName='DEFAULT', namingLoadCacheAtStart='false', metadata={preserved.register.source=SPRING_CLOUD}, registerEnabled=true, ip='**************', networkInterface='', port=8080, secure=false, accessKey='', secretKey=''}},
com.alibaba.nacos.api.exception.NacosException: no server available
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:521)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:501)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:496)
	at com.alibaba.nacos.client.naming.net.NamingProxy.deregisterService(NamingProxy.java:274)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:258)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:244)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:233)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:85)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.deregister(AbstractAutoServiceRegistration.java:236)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.stop(AbstractAutoServiceRegistration.java:251)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.destroy(AbstractAutoServiceRegistration.java:188)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:363)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:321)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:156)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:240)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:571)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:543)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1052)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:504)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1059)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1035)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1011)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:931)
2025-08-30 22:57:22.580 [Thread-37] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-08-30 22:57:22.582 [Thread-37] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'applicationTaskExecutor'
2025-08-30 22:57:22.597 [Thread-37] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2025-08-30 22:57:23.741 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - RSA_PKCS1_SHA_256 was destroying!
2025-08-30 22:57:23.742 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - your-kms-key-id was destroying!
2025-08-30 22:57:23.742 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - false was destroying!
2025-08-30 22:57:23.742 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - your-key-version-id was destroying!
2025-08-30 22:57:23.742 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - class com.alibaba.nacos.spring.context.annotation.config.NacosValueAnnotationBeanPostProcessor was destroying!
2025-08-30 22:57:23.742 [Thread-37] INFO  c.a.n.s.b.f.a.AnnotationNacosInjectedBeanPostProcessor - class com.alibaba.nacos.spring.beans.factory.annotation.AnnotationNacosInjectedBeanPostProcessor was destroying!
2025-08-30 22:57:23.743 [Thread-37] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-30 22:57:23.745 [Thread-37] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-30 22:57:29.584 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-08-30 22:57:29.600 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-08-30 22:57:30.241 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$5d3d85e3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:57:31.072 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-08-30 22:57:31.073 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-08-30 22:57:31.073 [main] INFO  c.c.o.a.service.OpenAuthApplication - No active profile set, falling back to default profiles: default
2025-08-30 22:57:32.316 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-08-30 22:57:32.859 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-30 22:57:32.862 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-30 22:57:32.926 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 50ms. Found 0 repository interfaces.
2025-08-30 22:57:33.084 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-08-30 22:57:33.627 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=d1c4090b-2810-3a5e-b670-eb417c19739b
2025-08-30 22:57:33.683 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IConditionFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:57:33.686 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IDictFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:57:33.690 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IEntityDataFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:57:33.694 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IMetadataAuthFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:57:33.698 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IMetadataFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:57:33.702 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.transaction.configuration.feign.ITransactionFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:57:33.799 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.DistributedLockConfiguration' of type [com.jarvis.cache.autoconfigure.DistributedLockConfiguration$$EnhancerBySpringCGLIB$$a8e99d12] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:57:33.820 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:57:33.832 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration$$EnhancerBySpringCGLIB$$563d4130] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:57:33.965 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:57:34.051 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:57:34.062 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoLoadCacheDistributedLock' of type [com.jarvis.cache.redis.SpringRedisLock] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:57:34.081 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoload.cache-com.jarvis.cache.autoconfigure.AutoloadCacheProperties' of type [com.jarvis.cache.autoconfigure.AutoloadCacheProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:57:34.082 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure' of type [com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure$$EnhancerBySpringCGLIB$$ef277b74] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:57:34.258 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$412382e6] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:57:34.494 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration$$EnhancerBySpringCGLIB$$76647c91] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:57:34.538 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$$EnhancerBySpringCGLIB$$7c6790ce] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:57:34.559 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheSerializer' of type [com.jarvis.cache.serializer.JdkSerializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:57:34.918 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2025-08-30 22:57:34.920 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2025-08-30 22:57:35.499 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheCacheManager' of type [com.jarvis.cache.redis.SpringRedisCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:57:35.523 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheScriptParser' of type [com.jarvis.cache.script.SpringELParser] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:57:35.551 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheHandler' of type [com.jarvis.cache.CacheHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:57:35.562 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteTransactionalInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:57:35.568 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:57:35.573 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:57:35.573 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:57:35.579 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheMethodInterceptor' of type [com.jarvis.cache.interceptor.CacheMethodInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:57:35.580 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:57:35.613 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$5d3d85e3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:57:36.224 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-08-30 22:57:36.239 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-30 22:57:36.246 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-30 22:57:36.247 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet Engine: Apache Tomcat/9.0.12
2025-08-30 22:57:36.257 [main] INFO  o.a.c.core.AprLifecycleListener - The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\bin\default;C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\bin;C:\windows\system32;C:\windows;C:\windows\system32;C:\windows;C:\windows\System32\Wbem;C:\windows\System32\WindowsPowerShell\v1.0\;C:\windows\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Users\<USER>\.jdks\temurin-1.8.0_302\bin;C:\Users\<USER>\.jdks\temurin-1.8.0_302\jre\bin;C:\Program Files\JetBrains\IntelliJ IDEA 2021.2.2\plugins\maven\lib\maven3\bin;C:\Program Files\Kubernetes\Minikube;;C:\minikube;C:\Program Files\Docker\Docker\resources\bin;C:\ProgramData\DockerDesktop\version-bin;C:\Program Files (x86)\Tencent\微信web开发者工具\dll;C:\Program Files\nodejs\;C:\Program Files\cursor\resources\app\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Users\<USER>\scoop\shims;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\IntelliJ IDEA 2021.2.2\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;.]
2025-08-30 22:57:36.512 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-30 22:57:36.512 [main] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 5417 ms
2025-08-30 22:57:38.689 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'characterEncodingFilter' to: [/*]
2025-08-30 22:57:38.690 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'webMvcMetricsFilter' to: [/*]
2025-08-30 22:57:38.691 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2025-08-30 22:57:38.691 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'formContentFilter' to: [/*]
2025-08-30 22:57:38.691 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'requestContextFilter' to: [/*]
2025-08-30 22:57:38.692 [main] INFO  o.s.b.w.s.DelegatingFilterProxyRegistrationBean - Mapping filter: 'springSecurityFilterChain' to: [/*]
2025-08-30 22:57:38.693 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'commonScopeFilter' to urls: [/*]
2025-08-30 22:57:38.693 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'httpTraceFilter' to: [/*]
2025-08-30 22:57:38.693 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'HTTPBasicAuthorizeAttribute' to urls: [/autoload-cache-ui.html, /autoload-cache/*]
2025-08-30 22:57:38.693 [main] INFO  o.s.b.w.s.ServletRegistrationBean - Servlet dispatcherServlet mapped to [/]
2025-08-30 22:57:39.754 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.caidaocloud.open.auth.service.infrastructure.persistence.po.OAuthClientPo".
2025-08-30 22:57:39.761 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.caidaocloud.open.auth.service.infrastructure.persistence.po.OAuthClientPo ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 22:57:40.244 [main] INFO  o.s.s.o.p.t.s.JwtAccessTokenConverter - Configured with RSA signing key
2025-08-30 22:57:40.261 [main] INFO  o.s.s.o.p.t.s.JwtAccessTokenConverter - Signing and verification RSA keys match
2025-08-30 22:57:41.134 [main] INFO  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 1367544e-e181-4631-8ccc-4415f1690a3c

2025-08-30 22:57:41.373 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Creating filter chain: OrRequestMatcher [requestMatchers=[Ant [pattern='/api/auth/open/v1/oauth/token'], Ant [pattern='/api/auth/open/v1/oauth/token_key'], Ant [pattern='/api/auth/open/v1/oauth/check_token']]], [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@edd0d90c, org.springframework.security.web.context.SecurityContextPersistenceFilter@d7121a2c, org.springframework.security.web.header.HeaderWriterFilter@932356c0, org.springframework.security.web.authentication.logout.LogoutFilter@b2d13276, org.springframework.security.oauth2.provider.client.ClientCredentialsTokenEndpointFilter@1dbde15c, com.caidaocloud.open.auth.service.infrastructure.config.oauth.OAuthClientCredentialsTokenEndpointFilter@f0b18199, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@9706db01, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5981f363, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@f01ee6ac, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@592ac8a9, org.springframework.security.web.session.SessionManagementFilter@e027786c, org.springframework.security.web.access.ExceptionTranslationFilter@c273386f, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@aa3ede79]
2025-08-30 22:57:41.379 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@d4f2ca37, org.springframework.security.web.context.SecurityContextPersistenceFilter@a8709104, org.springframework.security.web.header.HeaderWriterFilter@8da09024, org.springframework.security.web.authentication.logout.LogoutFilter@8af4dea4, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@ce80495a, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6bfc1fca, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@a456a16a, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@f65793da, org.springframework.security.web.session.SessionManagementFilter@5212509f, org.springframework.security.web.access.ExceptionTranslationFilter@49d452b, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@d06e9a9a]
2025-08-30 22:57:41.444 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-08-30 22:57:41.445 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-08-30 22:57:41.453 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-08-30 22:57:41.453 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-08-30 22:57:41.994 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2025-08-30 22:57:42.308 [main] INFO  o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService
2025-08-30 22:57:44.096 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-08-30 22:57:45.034 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-30 22:57:45.037 [main] INFO  o.a.tomcat.util.net.NioSelectorPool - Using a shared selector for servlet write/read
2025-08-30 22:57:45.065 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-08-30 22:57:45.119 [main] ERROR c.a.c.n.r.NacosServiceRegistry - nacos registry, caidao-open-auth-service register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='', endpoint='', namespace='', watchDelay=30000, logName='', service='caidao-open-auth-service', weight=1.0, clusterName='DEFAULT', namingLoadCacheAtStart='false', metadata={preserved.register.source=SPRING_CLOUD}, registerEnabled=true, ip='**************', networkInterface='', port=8080, secure=false, accessKey='', secretKey=''}},
com.alibaba.nacos.api.exception.NacosException: no server available
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:521)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:501)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:496)
	at com.alibaba.nacos.client.naming.net.NamingProxy.registerService(NamingProxy.java:249)
	at com.alibaba.nacos.client.naming.NacosNamingService.registerInstance(NacosNamingService.java:218)
	at com.alibaba.nacos.client.naming.NacosNamingService.registerInstance(NacosNamingService.java:207)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:61)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.register(AbstractAutoServiceRegistration.java:219)
	at com.alibaba.cloud.nacos.registry.NacosAutoServiceRegistration.register(NacosAutoServiceRegistration.java:74)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.start(AbstractAutoServiceRegistration.java:118)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.bind(AbstractAutoServiceRegistration.java:82)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.onApplicationEvent(AbstractAutoServiceRegistration.java:69)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.onApplicationEvent(AbstractAutoServiceRegistration.java:32)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:172)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:165)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:139)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:398)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:355)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.finishRefresh(ServletWebServerApplicationContext.java:164)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:549)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:140)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:775)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1260)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1248)
	at com.caidaocloud.open.auth.service.OpenAuthApplication.main(OpenAuthApplication.java:17)
2025-08-30 22:57:45.123 [main] INFO  c.c.o.a.service.OpenAuthApplication - Started OpenAuthApplication in 17.058 seconds (JVM running for 19.339)
2025-08-30 22:57:52.777 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-30 22:57:52.778 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-30 22:57:52.851 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 72 ms
2025-08-30 22:57:53.085 [http-nio-8080-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-30 22:57:53.226 [http-nio-8080-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-30 22:59:45.524 [Thread-26] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-08-30 22:59:45.525 [Thread-32] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-08-30 22:59:45.526 [Thread-32] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-08-30 22:59:45.527 [Thread-26] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-08-30 22:59:45.540 [Thread-37] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-08-30 22:59:45.545 [Thread-37] ERROR c.a.c.n.r.NacosServiceRegistry - ERR_NACOS_DEREGISTER, de-register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='', endpoint='', namespace='', watchDelay=30000, logName='', service='caidao-open-auth-service', weight=1.0, clusterName='DEFAULT', namingLoadCacheAtStart='false', metadata={preserved.register.source=SPRING_CLOUD}, registerEnabled=true, ip='**************', networkInterface='', port=8080, secure=false, accessKey='', secretKey=''}},
com.alibaba.nacos.api.exception.NacosException: no server available
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:521)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:501)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:496)
	at com.alibaba.nacos.client.naming.net.NamingProxy.deregisterService(NamingProxy.java:274)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:258)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:244)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:233)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:85)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.deregister(AbstractAutoServiceRegistration.java:236)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.stop(AbstractAutoServiceRegistration.java:251)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.destroy(AbstractAutoServiceRegistration.java:188)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:363)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:321)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:156)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:240)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:571)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:543)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1052)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:504)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1059)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1035)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1011)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:931)
2025-08-30 22:59:45.546 [Thread-37] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-08-30 22:59:45.548 [Thread-37] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'applicationTaskExecutor'
2025-08-30 22:59:45.574 [Thread-37] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2025-08-30 22:59:46.753 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - RSA_PKCS1_SHA_256 was destroying!
2025-08-30 22:59:46.753 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - your-kms-key-id was destroying!
2025-08-30 22:59:46.753 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - false was destroying!
2025-08-30 22:59:46.754 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - your-key-version-id was destroying!
2025-08-30 22:59:46.754 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - class com.alibaba.nacos.spring.context.annotation.config.NacosValueAnnotationBeanPostProcessor was destroying!
2025-08-30 22:59:46.754 [Thread-37] INFO  c.a.n.s.b.f.a.AnnotationNacosInjectedBeanPostProcessor - class com.alibaba.nacos.spring.beans.factory.annotation.AnnotationNacosInjectedBeanPostProcessor was destroying!
2025-08-30 22:59:46.754 [Thread-37] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-30 22:59:46.757 [Thread-37] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-30 22:59:56.039 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-08-30 22:59:56.044 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-08-30 22:59:56.586 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$e07ef37e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 22:59:57.335 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-08-30 22:59:57.336 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-08-30 22:59:57.337 [main] INFO  c.c.o.a.service.OpenAuthApplication - No active profile set, falling back to default profiles: default
2025-08-30 22:59:58.583 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-08-30 22:59:59.091 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-30 22:59:59.093 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-30 22:59:59.163 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 52ms. Found 0 repository interfaces.
2025-08-30 22:59:59.352 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-08-30 22:59:59.941 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=d1c4090b-2810-3a5e-b670-eb417c19739b
2025-08-30 23:00:00.034 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IConditionFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:00:00.040 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IDictFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:00:00.044 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IEntityDataFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:00:00.047 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IMetadataAuthFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:00:00.051 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IMetadataFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:00:00.057 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.transaction.configuration.feign.ITransactionFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:00:00.192 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.DistributedLockConfiguration' of type [com.jarvis.cache.autoconfigure.DistributedLockConfiguration$$EnhancerBySpringCGLIB$$2c2b0aad] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:00:00.229 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:00:00.248 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration$$EnhancerBySpringCGLIB$$d97eaecb] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:00:00.393 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:00:00.488 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:00:00.496 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoLoadCacheDistributedLock' of type [com.jarvis.cache.redis.SpringRedisLock] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:00:00.522 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoload.cache-com.jarvis.cache.autoconfigure.AutoloadCacheProperties' of type [com.jarvis.cache.autoconfigure.AutoloadCacheProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:00:00.523 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure' of type [com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure$$EnhancerBySpringCGLIB$$7268e90f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:00:00.684 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$c464f081] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:00:00.927 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration$$EnhancerBySpringCGLIB$$f9a5ea2c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:00:01.004 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$$EnhancerBySpringCGLIB$$ffa8fe69] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:00:01.025 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheSerializer' of type [com.jarvis.cache.serializer.JdkSerializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:00:01.355 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2025-08-30 23:00:01.357 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2025-08-30 23:00:01.906 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheCacheManager' of type [com.jarvis.cache.redis.SpringRedisCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:00:01.929 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheScriptParser' of type [com.jarvis.cache.script.SpringELParser] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:00:01.958 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheHandler' of type [com.jarvis.cache.CacheHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:00:01.968 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteTransactionalInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:00:01.977 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:00:01.981 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:00:01.982 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:00:01.985 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheMethodInterceptor' of type [com.jarvis.cache.interceptor.CacheMethodInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:00:01.986 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:00:02.016 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$e07ef37e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:00:02.650 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-08-30 23:00:02.669 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-30 23:00:02.680 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-30 23:00:02.680 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet Engine: Apache Tomcat/9.0.12
2025-08-30 23:00:02.694 [main] INFO  o.a.c.core.AprLifecycleListener - The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\bin\default;C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\bin;C:\windows\system32;C:\windows;C:\windows\system32;C:\windows;C:\windows\System32\Wbem;C:\windows\System32\WindowsPowerShell\v1.0\;C:\windows\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Users\<USER>\.jdks\temurin-1.8.0_302\bin;C:\Users\<USER>\.jdks\temurin-1.8.0_302\jre\bin;C:\Program Files\JetBrains\IntelliJ IDEA 2021.2.2\plugins\maven\lib\maven3\bin;C:\Program Files\Kubernetes\Minikube;;C:\minikube;C:\Program Files\Docker\Docker\resources\bin;C:\ProgramData\DockerDesktop\version-bin;C:\Program Files (x86)\Tencent\微信web开发者工具\dll;C:\Program Files\nodejs\;C:\Program Files\cursor\resources\app\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Users\<USER>\scoop\shims;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\IntelliJ IDEA 2021.2.2\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;.]
2025-08-30 23:00:02.946 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-30 23:00:02.947 [main] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 5590 ms
2025-08-30 23:00:05.437 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'characterEncodingFilter' to: [/*]
2025-08-30 23:00:05.438 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'webMvcMetricsFilter' to: [/*]
2025-08-30 23:00:05.439 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2025-08-30 23:00:05.439 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'formContentFilter' to: [/*]
2025-08-30 23:00:05.439 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'requestContextFilter' to: [/*]
2025-08-30 23:00:05.440 [main] INFO  o.s.b.w.s.DelegatingFilterProxyRegistrationBean - Mapping filter: 'springSecurityFilterChain' to: [/*]
2025-08-30 23:00:05.441 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'commonScopeFilter' to urls: [/*]
2025-08-30 23:00:05.441 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'httpTraceFilter' to: [/*]
2025-08-30 23:00:05.441 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'HTTPBasicAuthorizeAttribute' to urls: [/autoload-cache-ui.html, /autoload-cache/*]
2025-08-30 23:00:05.441 [main] INFO  o.s.b.w.s.ServletRegistrationBean - Servlet dispatcherServlet mapped to [/]
2025-08-30 23:00:06.500 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.caidaocloud.open.auth.service.infrastructure.persistence.po.OAuthClientPo".
2025-08-30 23:00:06.507 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.caidaocloud.open.auth.service.infrastructure.persistence.po.OAuthClientPo ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 23:00:07.043 [main] INFO  o.s.s.o.p.t.s.JwtAccessTokenConverter - Configured with RSA signing key
2025-08-30 23:00:07.053 [main] INFO  o.s.s.o.p.t.s.JwtAccessTokenConverter - Signing and verification RSA keys match
2025-08-30 23:00:07.940 [main] INFO  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: cdcc6718-34d5-4bb9-add3-d05a6c7228ce

2025-08-30 23:00:08.192 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Creating filter chain: OrRequestMatcher [requestMatchers=[Ant [pattern='/api/auth/open/v1/oauth/token'], Ant [pattern='/api/auth/open/v1/oauth/token_key'], Ant [pattern='/api/auth/open/v1/oauth/check_token']]], [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3d4e96c7, org.springframework.security.web.context.SecurityContextPersistenceFilter@de64ef3b, org.springframework.security.web.header.HeaderWriterFilter@f1013854, org.springframework.security.web.authentication.logout.LogoutFilter@f2c9cd6d, org.springframework.security.oauth2.provider.client.ClientCredentialsTokenEndpointFilter@431b205d, com.caidaocloud.open.auth.service.infrastructure.config.oauth.OAuthClientCredentialsTokenEndpointFilter@b73275f6, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@e4ef1e35, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@b5d7a7c9, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2494bd84, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@c2ddead3, org.springframework.security.web.session.SessionManagementFilter@72f79ac6, org.springframework.security.web.access.ExceptionTranslationFilter@8624c13e, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@cdb5d572]
2025-08-30 23:00:08.199 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@87a3ecca, org.springframework.security.web.context.SecurityContextPersistenceFilter@14613d8a, org.springframework.security.web.header.HeaderWriterFilter@7f50e447, org.springframework.security.web.authentication.logout.LogoutFilter@356fd79e, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@c22ccb86, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@9ae77cd5, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@ade1d67f, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@f8ac2d85, org.springframework.security.web.session.SessionManagementFilter@2efd13e3, org.springframework.security.web.access.ExceptionTranslationFilter@584e230f, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@ae5b0eff]
2025-08-30 23:00:08.258 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-08-30 23:00:08.258 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-08-30 23:00:08.265 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-08-30 23:00:08.265 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-08-30 23:00:08.810 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2025-08-30 23:00:09.153 [main] INFO  o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService
2025-08-30 23:00:11.059 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-08-30 23:00:12.014 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-30 23:00:12.018 [main] INFO  o.a.tomcat.util.net.NioSelectorPool - Using a shared selector for servlet write/read
2025-08-30 23:00:12.055 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-08-30 23:00:12.116 [main] ERROR c.a.c.n.r.NacosServiceRegistry - nacos registry, caidao-open-auth-service register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='', endpoint='', namespace='', watchDelay=30000, logName='', service='caidao-open-auth-service', weight=1.0, clusterName='DEFAULT', namingLoadCacheAtStart='false', metadata={preserved.register.source=SPRING_CLOUD}, registerEnabled=true, ip='**************', networkInterface='', port=8080, secure=false, accessKey='', secretKey=''}},
com.alibaba.nacos.api.exception.NacosException: no server available
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:521)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:501)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:496)
	at com.alibaba.nacos.client.naming.net.NamingProxy.registerService(NamingProxy.java:249)
	at com.alibaba.nacos.client.naming.NacosNamingService.registerInstance(NacosNamingService.java:218)
	at com.alibaba.nacos.client.naming.NacosNamingService.registerInstance(NacosNamingService.java:207)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:61)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.register(AbstractAutoServiceRegistration.java:219)
	at com.alibaba.cloud.nacos.registry.NacosAutoServiceRegistration.register(NacosAutoServiceRegistration.java:74)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.start(AbstractAutoServiceRegistration.java:118)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.bind(AbstractAutoServiceRegistration.java:82)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.onApplicationEvent(AbstractAutoServiceRegistration.java:69)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.onApplicationEvent(AbstractAutoServiceRegistration.java:32)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:172)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:165)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:139)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:398)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:355)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.finishRefresh(ServletWebServerApplicationContext.java:164)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:549)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:140)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:775)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1260)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1248)
	at com.caidaocloud.open.auth.service.OpenAuthApplication.main(OpenAuthApplication.java:17)
2025-08-30 23:00:12.119 [main] INFO  c.c.o.a.service.OpenAuthApplication - Started OpenAuthApplication in 17.21 seconds (JVM running for 19.191)
2025-08-30 23:00:39.472 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-30 23:00:39.473 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-30 23:00:39.517 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 44 ms
2025-08-30 23:00:39.842 [http-nio-8080-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-30 23:00:40.014 [http-nio-8080-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-30 23:03:31.529 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=2m51s401ms78µs300ns).
2025-08-30 23:03:31.859 [Thread-26] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-08-30 23:03:31.857 [Thread-32] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-08-30 23:03:31.864 [Thread-32] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-08-30 23:03:31.865 [Thread-26] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-08-30 23:03:31.883 [Thread-37] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-08-30 23:03:31.885 [Thread-37] ERROR c.a.c.n.r.NacosServiceRegistry - ERR_NACOS_DEREGISTER, de-register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='', endpoint='', namespace='', watchDelay=30000, logName='', service='caidao-open-auth-service', weight=1.0, clusterName='DEFAULT', namingLoadCacheAtStart='false', metadata={preserved.register.source=SPRING_CLOUD}, registerEnabled=true, ip='**************', networkInterface='', port=8080, secure=false, accessKey='', secretKey=''}},
com.alibaba.nacos.api.exception.NacosException: no server available
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:521)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:501)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:496)
	at com.alibaba.nacos.client.naming.net.NamingProxy.deregisterService(NamingProxy.java:274)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:258)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:244)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:233)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:85)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.deregister(AbstractAutoServiceRegistration.java:236)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.stop(AbstractAutoServiceRegistration.java:251)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.destroy(AbstractAutoServiceRegistration.java:188)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:363)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:321)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:156)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:240)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:571)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:543)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1052)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:504)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1059)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1035)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1011)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:931)
2025-08-30 23:03:31.886 [Thread-37] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-08-30 23:03:31.889 [Thread-37] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'applicationTaskExecutor'
2025-08-30 23:03:31.901 [Thread-37] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2025-08-30 23:03:33.179 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - RSA_PKCS1_SHA_256 was destroying!
2025-08-30 23:03:33.181 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - your-kms-key-id was destroying!
2025-08-30 23:03:33.182 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - false was destroying!
2025-08-30 23:03:33.183 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - your-key-version-id was destroying!
2025-08-30 23:03:33.184 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - class com.alibaba.nacos.spring.context.annotation.config.NacosValueAnnotationBeanPostProcessor was destroying!
2025-08-30 23:03:33.185 [Thread-37] INFO  c.a.n.s.b.f.a.AnnotationNacosInjectedBeanPostProcessor - class com.alibaba.nacos.spring.beans.factory.annotation.AnnotationNacosInjectedBeanPostProcessor was destroying!
2025-08-30 23:03:33.187 [Thread-37] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-30 23:03:33.199 [Thread-37] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-30 23:03:45.031 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-08-30 23:03:45.038 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-08-30 23:03:45.620 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$4390d4b4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:03:46.611 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-08-30 23:03:46.612 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-08-30 23:03:46.613 [main] INFO  c.c.o.a.service.OpenAuthApplication - No active profile set, falling back to default profiles: default
2025-08-30 23:03:48.173 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-08-30 23:03:48.649 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-30 23:03:48.653 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-30 23:03:48.721 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 43ms. Found 0 repository interfaces.
2025-08-30 23:03:48.880 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-08-30 23:03:49.440 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=d1c4090b-2810-3a5e-b670-eb417c19739b
2025-08-30 23:03:49.519 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IConditionFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:03:49.523 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IDictFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:03:49.526 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IEntityDataFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:03:49.530 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IMetadataAuthFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:03:49.532 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IMetadataFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:03:49.534 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.transaction.configuration.feign.ITransactionFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:03:49.646 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.DistributedLockConfiguration' of type [com.jarvis.cache.autoconfigure.DistributedLockConfiguration$$EnhancerBySpringCGLIB$$8f3cebe3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:03:49.671 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:03:49.685 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration$$EnhancerBySpringCGLIB$$3c909001] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:03:49.830 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:03:49.921 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:03:49.932 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoLoadCacheDistributedLock' of type [com.jarvis.cache.redis.SpringRedisLock] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:03:49.950 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoload.cache-com.jarvis.cache.autoconfigure.AutoloadCacheProperties' of type [com.jarvis.cache.autoconfigure.AutoloadCacheProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:03:49.951 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure' of type [com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure$$EnhancerBySpringCGLIB$$d57aca45] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:03:50.120 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$2776d1b7] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:03:50.386 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration$$EnhancerBySpringCGLIB$$5cb7cb62] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:03:50.426 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$$EnhancerBySpringCGLIB$$62badf9f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:03:50.447 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheSerializer' of type [com.jarvis.cache.serializer.JdkSerializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:03:50.789 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2025-08-30 23:03:50.791 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2025-08-30 23:03:51.366 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheCacheManager' of type [com.jarvis.cache.redis.SpringRedisCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:03:51.393 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheScriptParser' of type [com.jarvis.cache.script.SpringELParser] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:03:51.425 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheHandler' of type [com.jarvis.cache.CacheHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:03:51.436 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteTransactionalInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:03:51.446 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:03:51.454 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:03:51.455 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:03:51.463 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheMethodInterceptor' of type [com.jarvis.cache.interceptor.CacheMethodInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:03:51.464 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:03:51.489 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$4390d4b4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:03:52.182 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-08-30 23:03:52.202 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-30 23:03:52.210 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-30 23:03:52.210 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet Engine: Apache Tomcat/9.0.12
2025-08-30 23:03:52.217 [main] INFO  o.a.c.core.AprLifecycleListener - The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\bin\default;C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\bin;C:\windows\system32;C:\windows;C:\windows\system32;C:\windows;C:\windows\System32\Wbem;C:\windows\System32\WindowsPowerShell\v1.0\;C:\windows\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Users\<USER>\.jdks\temurin-1.8.0_302\bin;C:\Users\<USER>\.jdks\temurin-1.8.0_302\jre\bin;C:\Program Files\JetBrains\IntelliJ IDEA 2021.2.2\plugins\maven\lib\maven3\bin;C:\Program Files\Kubernetes\Minikube;;C:\minikube;C:\Program Files\Docker\Docker\resources\bin;C:\ProgramData\DockerDesktop\version-bin;C:\Program Files (x86)\Tencent\微信web开发者工具\dll;C:\Program Files\nodejs\;C:\Program Files\cursor\resources\app\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Users\<USER>\scoop\shims;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\IntelliJ IDEA 2021.2.2\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;.]
2025-08-30 23:03:52.500 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-30 23:03:52.500 [main] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 5843 ms
2025-08-30 23:03:55.726 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'characterEncodingFilter' to: [/*]
2025-08-30 23:03:55.727 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'webMvcMetricsFilter' to: [/*]
2025-08-30 23:03:55.728 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2025-08-30 23:03:55.728 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'formContentFilter' to: [/*]
2025-08-30 23:03:55.729 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'requestContextFilter' to: [/*]
2025-08-30 23:03:55.730 [main] INFO  o.s.b.w.s.DelegatingFilterProxyRegistrationBean - Mapping filter: 'springSecurityFilterChain' to: [/*]
2025-08-30 23:03:55.731 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'commonScopeFilter' to urls: [/*]
2025-08-30 23:03:55.731 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'httpTraceFilter' to: [/*]
2025-08-30 23:03:55.732 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'HTTPBasicAuthorizeAttribute' to urls: [/autoload-cache-ui.html, /autoload-cache/*]
2025-08-30 23:03:55.732 [main] INFO  o.s.b.w.s.ServletRegistrationBean - Servlet dispatcherServlet mapped to [/]
2025-08-30 23:03:57.535 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.caidaocloud.open.auth.service.infrastructure.persistence.po.OAuthClientPo".
2025-08-30 23:03:57.544 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.caidaocloud.open.auth.service.infrastructure.persistence.po.OAuthClientPo ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 23:03:58.210 [main] INFO  o.s.s.o.p.t.s.JwtAccessTokenConverter - Configured with RSA signing key
2025-08-30 23:03:58.237 [main] INFO  o.s.s.o.p.t.s.JwtAccessTokenConverter - Signing and verification RSA keys match
2025-08-30 23:03:59.631 [main] INFO  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 34c079df-3a0c-4601-ae36-672f0bd6b810

2025-08-30 23:03:59.978 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Creating filter chain: OrRequestMatcher [requestMatchers=[Ant [pattern='/api/auth/open/v1/oauth/token'], Ant [pattern='/api/auth/open/v1/oauth/token_key'], Ant [pattern='/api/auth/open/v1/oauth/check_token']]], [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@b759e8a9, org.springframework.security.web.context.SecurityContextPersistenceFilter@f9b64cd2, org.springframework.security.web.header.HeaderWriterFilter@3532a13f, org.springframework.security.web.authentication.logout.LogoutFilter@22fbec9c, org.springframework.security.oauth2.provider.client.ClientCredentialsTokenEndpointFilter@49195c00, com.caidaocloud.open.auth.service.infrastructure.config.oauth.OAuthClientCredentialsTokenEndpointFilter@9c9cb123, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@1f51af26, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@91cbc674, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@8d308b41, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@ea1456d5, org.springframework.security.web.session.SessionManagementFilter@3e31926d, org.springframework.security.web.access.ExceptionTranslationFilter@2209222d, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@9e848321]
2025-08-30 23:03:59.990 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4713990f, org.springframework.security.web.context.SecurityContextPersistenceFilter@bf90fefb, org.springframework.security.web.header.HeaderWriterFilter@47bb9fd4, org.springframework.security.web.authentication.logout.LogoutFilter@b0821b34, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@52af2b3b, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@ae358df1, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@15fabaad, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@da31bc15, org.springframework.security.web.session.SessionManagementFilter@e68543c4, org.springframework.security.web.access.ExceptionTranslationFilter@f59fe044, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@2636e3c8]
2025-08-30 23:04:00.111 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-08-30 23:04:00.112 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-08-30 23:04:00.124 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-08-30 23:04:00.125 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-08-30 23:04:01.067 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2025-08-30 23:04:01.582 [main] INFO  o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService
2025-08-30 23:04:03.581 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-08-30 23:04:04.808 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-30 23:04:04.811 [main] INFO  o.a.tomcat.util.net.NioSelectorPool - Using a shared selector for servlet write/read
2025-08-30 23:04:04.837 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-08-30 23:04:04.924 [main] ERROR c.a.c.n.r.NacosServiceRegistry - nacos registry, caidao-open-auth-service register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='', endpoint='', namespace='', watchDelay=30000, logName='', service='caidao-open-auth-service', weight=1.0, clusterName='DEFAULT', namingLoadCacheAtStart='false', metadata={preserved.register.source=SPRING_CLOUD}, registerEnabled=true, ip='**************', networkInterface='', port=8080, secure=false, accessKey='', secretKey=''}},
com.alibaba.nacos.api.exception.NacosException: no server available
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:521)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:501)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:496)
	at com.alibaba.nacos.client.naming.net.NamingProxy.registerService(NamingProxy.java:249)
	at com.alibaba.nacos.client.naming.NacosNamingService.registerInstance(NacosNamingService.java:218)
	at com.alibaba.nacos.client.naming.NacosNamingService.registerInstance(NacosNamingService.java:207)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:61)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.register(AbstractAutoServiceRegistration.java:219)
	at com.alibaba.cloud.nacos.registry.NacosAutoServiceRegistration.register(NacosAutoServiceRegistration.java:74)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.start(AbstractAutoServiceRegistration.java:118)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.bind(AbstractAutoServiceRegistration.java:82)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.onApplicationEvent(AbstractAutoServiceRegistration.java:69)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.onApplicationEvent(AbstractAutoServiceRegistration.java:32)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:172)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:165)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:139)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:398)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:355)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.finishRefresh(ServletWebServerApplicationContext.java:164)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:549)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:140)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:775)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1260)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1248)
	at com.caidaocloud.open.auth.service.OpenAuthApplication.main(OpenAuthApplication.java:17)
2025-08-30 23:04:04.931 [main] INFO  c.c.o.a.service.OpenAuthApplication - Started OpenAuthApplication in 21.17 seconds (JVM running for 23.157)
2025-08-30 23:04:08.035 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-30 23:04:08.035 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-30 23:04:08.092 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 57 ms
2025-08-30 23:04:08.455 [http-nio-8080-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-30 23:04:08.624 [http-nio-8080-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-30 23:04:32.039 [Thread-26] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-08-30 23:04:32.039 [Thread-32] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-08-30 23:04:32.040 [Thread-32] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-08-30 23:04:32.040 [Thread-26] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-08-30 23:04:32.046 [Thread-37] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-08-30 23:04:32.047 [Thread-37] ERROR c.a.c.n.r.NacosServiceRegistry - ERR_NACOS_DEREGISTER, de-register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='', endpoint='', namespace='', watchDelay=30000, logName='', service='caidao-open-auth-service', weight=1.0, clusterName='DEFAULT', namingLoadCacheAtStart='false', metadata={preserved.register.source=SPRING_CLOUD}, registerEnabled=true, ip='**************', networkInterface='', port=8080, secure=false, accessKey='', secretKey=''}},
com.alibaba.nacos.api.exception.NacosException: no server available
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:521)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:501)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:496)
	at com.alibaba.nacos.client.naming.net.NamingProxy.deregisterService(NamingProxy.java:274)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:258)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:244)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:233)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:85)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.deregister(AbstractAutoServiceRegistration.java:236)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.stop(AbstractAutoServiceRegistration.java:251)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.destroy(AbstractAutoServiceRegistration.java:188)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:363)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:321)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:156)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:240)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:571)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:543)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1052)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:504)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1059)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1035)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1011)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:931)
2025-08-30 23:04:32.047 [Thread-37] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-08-30 23:04:32.050 [Thread-37] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'applicationTaskExecutor'
2025-08-30 23:04:32.066 [Thread-37] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2025-08-30 23:04:33.302 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - RSA_PKCS1_SHA_256 was destroying!
2025-08-30 23:04:33.303 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - your-kms-key-id was destroying!
2025-08-30 23:04:33.303 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - false was destroying!
2025-08-30 23:04:33.303 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - your-key-version-id was destroying!
2025-08-30 23:04:33.303 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - class com.alibaba.nacos.spring.context.annotation.config.NacosValueAnnotationBeanPostProcessor was destroying!
2025-08-30 23:04:33.303 [Thread-37] INFO  c.a.n.s.b.f.a.AnnotationNacosInjectedBeanPostProcessor - class com.alibaba.nacos.spring.beans.factory.annotation.AnnotationNacosInjectedBeanPostProcessor was destroying!
2025-08-30 23:04:33.303 [Thread-37] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-30 23:04:33.305 [Thread-37] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-30 23:04:42.706 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-08-30 23:04:42.712 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-08-30 23:04:43.268 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$10ddd6c8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:04:44.006 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-08-30 23:04:44.006 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-08-30 23:04:44.007 [main] INFO  c.c.o.a.service.OpenAuthApplication - No active profile set, falling back to default profiles: default
2025-08-30 23:04:45.416 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-08-30 23:04:45.910 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-30 23:04:45.914 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-30 23:04:45.977 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 45ms. Found 0 repository interfaces.
2025-08-30 23:04:46.140 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-08-30 23:04:46.728 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=d1c4090b-2810-3a5e-b670-eb417c19739b
2025-08-30 23:04:46.806 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IConditionFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:04:46.811 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IDictFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:04:46.815 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IEntityDataFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:04:46.819 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IMetadataAuthFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:04:46.821 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IMetadataFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:04:46.823 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.transaction.configuration.feign.ITransactionFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:04:46.926 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.DistributedLockConfiguration' of type [com.jarvis.cache.autoconfigure.DistributedLockConfiguration$$EnhancerBySpringCGLIB$$5c89edf7] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:04:46.955 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:04:46.965 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration$$EnhancerBySpringCGLIB$$9dd9215] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:04:47.114 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:04:47.212 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:04:47.221 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoLoadCacheDistributedLock' of type [com.jarvis.cache.redis.SpringRedisLock] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:04:47.237 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoload.cache-com.jarvis.cache.autoconfigure.AutoloadCacheProperties' of type [com.jarvis.cache.autoconfigure.AutoloadCacheProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:04:47.238 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure' of type [com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure$$EnhancerBySpringCGLIB$$a2c7cc59] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:04:47.414 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$f4c3d3cb] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:04:47.691 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration$$EnhancerBySpringCGLIB$$2a04cd76] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:04:47.726 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$$EnhancerBySpringCGLIB$$3007e1b3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:04:47.749 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheSerializer' of type [com.jarvis.cache.serializer.JdkSerializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:04:48.092 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2025-08-30 23:04:48.094 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2025-08-30 23:04:48.659 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheCacheManager' of type [com.jarvis.cache.redis.SpringRedisCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:04:48.685 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheScriptParser' of type [com.jarvis.cache.script.SpringELParser] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:04:48.714 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheHandler' of type [com.jarvis.cache.CacheHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:04:48.724 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteTransactionalInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:04:48.735 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:04:48.739 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:04:48.740 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:04:48.745 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheMethodInterceptor' of type [com.jarvis.cache.interceptor.CacheMethodInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:04:48.747 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:04:48.782 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$10ddd6c8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-30 23:04:49.445 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-08-30 23:04:49.460 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-30 23:04:49.468 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-30 23:04:49.468 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet Engine: Apache Tomcat/9.0.12
2025-08-30 23:04:49.480 [main] INFO  o.a.c.core.AprLifecycleListener - The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\bin\default;C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\bin;C:\windows\system32;C:\windows;C:\windows\system32;C:\windows;C:\windows\System32\Wbem;C:\windows\System32\WindowsPowerShell\v1.0\;C:\windows\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Users\<USER>\.jdks\temurin-1.8.0_302\bin;C:\Users\<USER>\.jdks\temurin-1.8.0_302\jre\bin;C:\Program Files\JetBrains\IntelliJ IDEA 2021.2.2\plugins\maven\lib\maven3\bin;C:\Program Files\Kubernetes\Minikube;;C:\minikube;C:\Program Files\Docker\Docker\resources\bin;C:\ProgramData\DockerDesktop\version-bin;C:\Program Files (x86)\Tencent\微信web开发者工具\dll;C:\Program Files\nodejs\;C:\Program Files\cursor\resources\app\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Users\<USER>\scoop\shims;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\IntelliJ IDEA 2021.2.2\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;.]
2025-08-30 23:04:49.757 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-30 23:04:49.758 [main] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 5713 ms
2025-08-30 23:04:52.190 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'characterEncodingFilter' to: [/*]
2025-08-30 23:04:52.190 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'webMvcMetricsFilter' to: [/*]
2025-08-30 23:04:52.191 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2025-08-30 23:04:52.191 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'formContentFilter' to: [/*]
2025-08-30 23:04:52.191 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'requestContextFilter' to: [/*]
2025-08-30 23:04:52.192 [main] INFO  o.s.b.w.s.DelegatingFilterProxyRegistrationBean - Mapping filter: 'springSecurityFilterChain' to: [/*]
2025-08-30 23:04:52.193 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'commonScopeFilter' to urls: [/*]
2025-08-30 23:04:52.193 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'httpTraceFilter' to: [/*]
2025-08-30 23:04:52.193 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'HTTPBasicAuthorizeAttribute' to urls: [/autoload-cache-ui.html, /autoload-cache/*]
2025-08-30 23:04:52.193 [main] INFO  o.s.b.w.s.ServletRegistrationBean - Servlet dispatcherServlet mapped to [/]
2025-08-30 23:04:53.210 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.caidaocloud.open.auth.service.infrastructure.persistence.po.OAuthClientPo".
2025-08-30 23:04:53.220 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.caidaocloud.open.auth.service.infrastructure.persistence.po.OAuthClientPo ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-30 23:04:53.690 [main] INFO  o.s.s.o.p.t.s.JwtAccessTokenConverter - Configured with RSA signing key
2025-08-30 23:04:53.710 [main] INFO  o.s.s.o.p.t.s.JwtAccessTokenConverter - Signing and verification RSA keys match
2025-08-30 23:04:54.581 [main] INFO  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 87742252-b85f-4bed-be33-9d45ac1b2c3b

2025-08-30 23:04:54.820 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Creating filter chain: OrRequestMatcher [requestMatchers=[Ant [pattern='/api/auth/open/v1/oauth/token'], Ant [pattern='/api/auth/open/v1/oauth/token_key'], Ant [pattern='/api/auth/open/v1/oauth/check_token']]], [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@d3fff54a, org.springframework.security.web.context.SecurityContextPersistenceFilter@3469de93, org.springframework.security.web.header.HeaderWriterFilter@4c99adb4, org.springframework.security.web.authentication.logout.LogoutFilter@b830a8ce, org.springframework.security.oauth2.provider.client.ClientCredentialsTokenEndpointFilter@c75fbe75, com.caidaocloud.open.auth.service.infrastructure.config.oauth.OAuthClientCredentialsTokenEndpointFilter@2a0e74ff, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@db1f6066, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@fd74714, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@e8088065, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@3b53ade9, org.springframework.security.web.session.SessionManagementFilter@e5c520b4, org.springframework.security.web.access.ExceptionTranslationFilter@c81bf01e, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@bfcd7f62]
2025-08-30 23:04:54.828 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@bb29291, org.springframework.security.web.context.SecurityContextPersistenceFilter@75310f85, org.springframework.security.web.header.HeaderWriterFilter@6d1a9b39, org.springframework.security.web.authentication.logout.LogoutFilter@8230a1f9, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@c283eff3, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@fc9d0e82, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2ac8b6c0, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@d6d72ed1, org.springframework.security.web.session.SessionManagementFilter@9250a39b, org.springframework.security.web.access.ExceptionTranslationFilter@cfbc3cbc, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@70dcafc1]
2025-08-30 23:04:54.884 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-08-30 23:04:54.885 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-08-30 23:04:54.895 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-08-30 23:04:54.895 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-08-30 23:04:55.382 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2025-08-30 23:04:55.697 [main] INFO  o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService
2025-08-30 23:04:57.777 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-08-30 23:04:58.751 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-30 23:04:58.756 [main] INFO  o.a.tomcat.util.net.NioSelectorPool - Using a shared selector for servlet write/read
2025-08-30 23:04:58.780 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-08-30 23:04:58.833 [main] ERROR c.a.c.n.r.NacosServiceRegistry - nacos registry, caidao-open-auth-service register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='', endpoint='', namespace='', watchDelay=30000, logName='', service='caidao-open-auth-service', weight=1.0, clusterName='DEFAULT', namingLoadCacheAtStart='false', metadata={preserved.register.source=SPRING_CLOUD}, registerEnabled=true, ip='**************', networkInterface='', port=8080, secure=false, accessKey='', secretKey=''}},
com.alibaba.nacos.api.exception.NacosException: no server available
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:521)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:501)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:496)
	at com.alibaba.nacos.client.naming.net.NamingProxy.registerService(NamingProxy.java:249)
	at com.alibaba.nacos.client.naming.NacosNamingService.registerInstance(NacosNamingService.java:218)
	at com.alibaba.nacos.client.naming.NacosNamingService.registerInstance(NacosNamingService.java:207)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:61)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.register(AbstractAutoServiceRegistration.java:219)
	at com.alibaba.cloud.nacos.registry.NacosAutoServiceRegistration.register(NacosAutoServiceRegistration.java:74)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.start(AbstractAutoServiceRegistration.java:118)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.bind(AbstractAutoServiceRegistration.java:82)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.onApplicationEvent(AbstractAutoServiceRegistration.java:69)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.onApplicationEvent(AbstractAutoServiceRegistration.java:32)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:172)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:165)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:139)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:398)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:355)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.finishRefresh(ServletWebServerApplicationContext.java:164)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:549)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:140)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:775)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1260)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1248)
	at com.caidaocloud.open.auth.service.OpenAuthApplication.main(OpenAuthApplication.java:17)
2025-08-30 23:04:58.837 [main] INFO  c.c.o.a.service.OpenAuthApplication - Started OpenAuthApplication in 17.388 seconds (JVM running for 19.485)
2025-08-30 23:05:23.204 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-30 23:05:23.204 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-30 23:05:23.233 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 29 ms
2025-08-30 23:05:23.618 [http-nio-8080-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-30 23:05:23.769 [http-nio-8080-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
