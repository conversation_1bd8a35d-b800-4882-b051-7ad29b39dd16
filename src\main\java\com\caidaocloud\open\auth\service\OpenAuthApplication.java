package com.caidaocloud.open.auth.service;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableAuthorizationServer;

/**
 * Caidao OAuth2 Authorization Service Application (Client Credentials Only)
 *
 * <AUTHOR>
 */
@SpringBootApplication
@EnableAuthorizationServer
public class OpenAuthApplication {

    public static void main(String[] args) {
        SpringApplication.run(OpenAuthApplication.class, args);
    }

}
