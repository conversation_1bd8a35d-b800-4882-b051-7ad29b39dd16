package com.caidaocloud.open.auth.service.infrastructure.config.aliyun.request;

import com.aliyun.kms20160120.Client;
import com.aliyun.kms20160120.models.CreateSecretRequest;
import com.aliyun.kms20160120.models.CreateSecretResponse;

/**
 *
 * <AUTHOR>
 * @date 2025/9/4
 */
public class CreateSecretRequestWrapper extends KmsRequestWrapper<CreateSecretRequest, CreateSecretResponse> {
	public CreateSecretRequestWrapper(Client kmsClient) {
		super(kmsClient, "createSecret");
	}
}
