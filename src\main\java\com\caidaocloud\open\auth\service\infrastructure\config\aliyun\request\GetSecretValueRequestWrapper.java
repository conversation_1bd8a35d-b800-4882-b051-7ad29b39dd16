package com.caidaocloud.open.auth.service.infrastructure.config.aliyun.request;

import com.aliyun.kms20160120.Client;
import com.aliyun.kms20160120.models.GetSecretValueRequest;
import com.aliyun.kms20160120.models.GetSecretValueResponse;

/**
 *
 * <AUTHOR>
 * @date 2025/9/4
 */
public class GetSecretValueRequestWrapper extends KmsRequestWrapper<GetSecretValueRequest, GetSecretValueResponse> {
	public GetSecretValueRequestWrapper(Client kmsClient) {
		super(kmsClient, "getSecretValue");
	}
}
