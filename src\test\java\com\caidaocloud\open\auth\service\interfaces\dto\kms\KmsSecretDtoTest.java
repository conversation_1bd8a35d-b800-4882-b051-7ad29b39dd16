package com.caidaocloud.open.auth.service.interfaces.dto.kms;

import org.junit.Test;

import static org.junit.Assert.*;

/**
 * KMS密钥DTO测试
 * 
 * <AUTHOR>
 * @date 2025/9/5
 */
public class KmsSecretDtoTest {

    @Test
    public void testKmsSecretRequestDto_ValidateForCreate_Success() {
        KmsSecretRequestDto requestDto = new KmsSecretRequestDto();
        requestDto.setSecretName("test-secret");
        requestDto.setVersionId("v1");
        requestDto.setSecretData("test-data");

        // 应该不抛出异常
        requestDto.validateForCreate();
    }

    @Test(expected = IllegalArgumentException.class)
    public void testKmsSecretRequestDto_ValidateForCreate_MissingVersionId() {
        KmsSecretRequestDto requestDto = new KmsSecretRequestDto();
        requestDto.setSecretName("test-secret");
        requestDto.setSecretData("test-data");

        requestDto.validateForCreate();
    }

    @Test(expected = IllegalArgumentException.class)
    public void testKmsSecretRequestDto_ValidateForCreate_MissingSecretData() {
        KmsSecretRequestDto requestDto = new KmsSecretRequestDto();
        requestDto.setSecretName("test-secret");
        requestDto.setVersionId("v1");

        requestDto.validateForCreate();
    }

    @Test
    public void testKmsSecretRequestDto_ValidateForPut_Success() {
        KmsSecretRequestDto requestDto = new KmsSecretRequestDto();
        requestDto.setSecretName("test-secret");
        requestDto.setVersionId("v2");
        requestDto.setSecretData("updated-data");

        // 应该不抛出异常
        requestDto.validateForPut();
    }

    @Test
    public void testKmsSecretResponseDto_CreateSuccess() {
        KmsSecretResponseDto response = KmsSecretResponseDto.createSuccess("test-secret", "v1");

        assertEquals("test-secret", response.getSecretName());
        assertEquals("v1", response.getVersionId());
        assertTrue(response.getSuccess());
        assertEquals("密钥创建成功", response.getMessage());
        assertNull(response.getSecretData());
    }

    @Test
    public void testKmsSecretResponseDto_GetSuccess() {
        KmsSecretResponseDto response = KmsSecretResponseDto.getSuccess("test-secret", "v1", "secret-data");

        assertEquals("test-secret", response.getSecretName());
        assertEquals("v1", response.getVersionId());
        assertEquals("secret-data", response.getSecretData());
        assertTrue(response.getSuccess());
        assertEquals("密钥获取成功", response.getMessage());
    }

    @Test
    public void testKmsSecretResponseDto_PutSuccess() {
        KmsSecretResponseDto response = KmsSecretResponseDto.putSuccess("test-secret", "v2");

        assertEquals("test-secret", response.getSecretName());
        assertEquals("v2", response.getVersionId());
        assertTrue(response.getSuccess());
        assertEquals("密钥更新成功", response.getMessage());
        assertNull(response.getSecretData());
    }

    @Test
    public void testKmsSecretResponseDto_Failure() {
        KmsSecretResponseDto response = KmsSecretResponseDto.failure("test-secret", "操作失败");

        assertEquals("test-secret", response.getSecretName());
        assertFalse(response.getSuccess());
        assertEquals("操作失败", response.getMessage());
        assertNull(response.getVersionId());
        assertNull(response.getSecretData());
    }
}
