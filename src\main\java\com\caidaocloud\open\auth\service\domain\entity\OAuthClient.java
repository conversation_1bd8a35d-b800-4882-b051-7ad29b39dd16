package com.caidaocloud.open.auth.service.domain.entity;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * OAuth Client Domain Model
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class OAuthClient {

    private String clientId;
    private String tenantId;
    private String clientSecret;
    private String scope;
    private String authorities;
    private Integer accessTokenValidity;
    private Integer refreshTokenValidity;
    private String authorizedGrantTypes = "";

    public OAuthClient(String clientId, String clientSecret, String scope) {
        this.clientId = clientId;
        this.clientSecret = clientSecret;
        this.scope = scope;
        this.accessTokenValidity = 3600; // 1 hour default
    }

    public boolean hasScope(String requestedScope) {
        if (scope == null || scope.isEmpty()) {
            return false;
        }
        List<String> scopes = Arrays.asList(scope.split(","));
        return scopes.contains(requestedScope);
    }

}
