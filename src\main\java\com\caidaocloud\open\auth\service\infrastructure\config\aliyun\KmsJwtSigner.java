package com.caidaocloud.open.auth.service.infrastructure.config.aliyun;

import java.nio.charset.StandardCharsets;
import java.util.Map;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.aliyun.kms20160120.Client;
import com.aliyun.kms20160120.models.AsymmetricSignRequest;
import com.aliyun.kms20160120.models.AsymmetricVerifyRequest;
import com.aliyun.teaopenapi.models.Config;
import com.caidaocloud.open.auth.service.infrastructure.config.aliyun.request.AsymmetricSignRequestWrapper;
import com.caidaocloud.open.auth.service.infrastructure.config.aliyun.request.AsymmetricVerifyRequestWrapper;
import com.aliyun.teautil.models.RuntimeOptions;
import com.caidaocloud.open.auth.service.infrastructure.util.SHA256Util;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.Sha256Util;
import com.googlecode.totallylazy.Maps;
import io.jsonwebtoken.JwsHeader;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import org.springframework.security.jwt.crypto.sign.Signer;
import org.springframework.stereotype.Component;
import org.springframework.util.Base64Utils;

@Component
@Slf4j
public class KmsJwtSigner implements Signer {

    private final Client kmsClient;
    @NacosValue(value = "${aliyun.kms.key-id}", autoRefreshed = true)
    private String keyId;
    @NacosValue(value = "${aliyun.kms.key-version-id}", autoRefreshed = true)
    private String keyVersionId;
    @NacosValue(value = "${aliyun.kms.alg}", autoRefreshed = true)
    private String alg;

    public KmsJwtSigner(AliyunConfig aliyunConfig) throws Exception {
        Config config = new Config()
                .setAccessKeyId(aliyunConfig.getAccessKeyId())
                .setAccessKeySecret(aliyunConfig.getAccessKeySecret())
                .setEndpoint(aliyunConfig.getEndpoint());
        this.kmsClient = new Client(config);
    }





    /**
     * 非对称签名
     *
     * @param digest 待签名的摘要
     * @return 签名结果
     */
    private String sign(String digest) {
        AsymmetricSignRequestWrapper wrapper = new AsymmetricSignRequestWrapper(kmsClient);
        AsymmetricSignRequest signRequest = wrapper.builder().build()
                .setKeyId(keyId)
                .setKeyVersionId(keyVersionId)
                .setDigest(digest)
                .setAlgorithm(alg);
        return wrapper.execute(signRequest).getBody().getValue();
    }

    /**
     * 非对称验签
     *
     * @param keyId        密钥ID
     * @param keyVersionId 密钥版本ID
     * @param alg          算法
     * @param digest       摘要
     * @param signature    签名值
     * @return 验签结果
     */
    private boolean verify(String keyId, String keyVersionId, String alg, String digest, String signature) {
        AsymmetricVerifyRequestWrapper wrapper = new AsymmetricVerifyRequestWrapper(kmsClient);
        AsymmetricVerifyRequest request = wrapper.builder().build()
                .setKeyId(keyId)
                .setKeyVersionId(keyVersionId)
                .setAlgorithm(alg)
                .setDigest(digest)
                .setValue(signature);
        return wrapper.execute(request).getBody().getValue();
    }



    @SneakyThrows
    public String sign(Map<String, Object> claims) {
        String json = FastjsonUtil.toJson(claims);
        String digest = SHA256Util.base64Encode(json);
        return sign(digest);
    }

    public boolean verify(JwsHeader<?> header, String payload, String signature) {
        String keyId = header.getKeyId();
        String algorithm = (String) header.get("keyAlg");;
        String keyVersionId = (String) header.get("keyVersionId");
        return verify(keyId, keyVersionId, algorithm, payload, signature);
    }

    @SneakyThrows
    @Override
    public byte[] sign(byte[] bytes) {
        return sign(SHA256Util.base64Encode(bytes)).getBytes(StandardCharsets.UTF_8);
    }

    @Override
    public String algorithm() {
        return "SHA256withRSA";
    }

    public Map<String, String> headerParams() {
        return Maps.map("kid", keyId, "keyVersionId", keyVersionId, "keyAlg", alg);
    }
}