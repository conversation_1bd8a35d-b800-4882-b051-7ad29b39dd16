package com.caidaocloud.open.auth.service.interfaces.dto.kms;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 更新密钥值请求DTO
 * 
 * <AUTHOR>
 * @date 2025/9/5
 */
@Data
public class PutSecretValueRequestDto {

    /**
     * 版本ID
     */
    @NotBlank(message = "版本ID不能为空")
    @Size(min = 1, max = 64, message = "版本ID长度必须在1-64个字符之间")
    private String versionId;

    /**
     * 密钥名称
     */
    @NotBlank(message = "密钥名称不能为空")
    @Size(min = 1, max = 192, message = "密钥名称长度必须在1-192个字符之间")
    private String secretName;

    /**
     * 密钥数据
     */
    @NotBlank(message = "密钥数据不能为空")
    @Size(max = 65536, message = "密钥数据长度不能超过65536个字符")
    private String secretData;
}
