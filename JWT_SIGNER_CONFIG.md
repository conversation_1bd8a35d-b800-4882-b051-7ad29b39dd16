# JWT签名服务配置说明

## 概述

本项目已完全重构JWT签名服务架构，支持通过配置文件选择不同的签名方式：
- **简单对称加密** (simple): 使用HMAC-SHA256算法
- **阿里云KMS** (aliyun-kms): 使用阿里云密钥管理服务进行非对称签名

## 架构改进

### 新增组件
1. **JwtSignerService接口** - 统一的JWT签名服务接口
2. **SimpleJwtSignerService** - 简单对称加密实现
3. **SimpleJwtAccessTokenConverter** - 简单对称加密的访问令牌转换器
4. **AliyunKmsJwtAccessTokenConverter** - 阿里云KMS的访问令牌转换器
5. **JwtAccessTokenConverterConfig** - 自动配置类，根据配置选择转换器

### 配置化注入
- Spring会根据`jwt.signer.type`配置自动注入相应的`JwtAccessTokenConverter`
- OAuth2服务器自动使用配置的转换器，无需手动切换代码

## 配置方式

### 1. 简单对称加密配置 (推荐用于开发和测试)

在 `application.yml` 中配置：

```yaml
jwt:
  signer:
    # 选择简单对称加密
    type: simple
    simple:
      # 配置密钥，建议使用长度至少32字符的强密钥
      secret-key: ${JWT_SECRET_KEY:your-very-long-secret-key-for-hmac-sha256-encryption}
```

### 2. 阿里云KMS配置 (推荐用于生产环境)

在 `application.yml` 中配置：

```yaml
jwt:
  signer:
    # 选择阿里云KMS
    type: aliyun-kms

# 阿里云配置
aliyun:
  access-key-id: ${ALIYUN_ACCESS_KEY_ID:your-access-key-id}
  access-key-secret: ${ALIYUN_ACCESS_KEY_SECRET:your-access-key-secret}
  region-id: ${ALIYUN_REGION_ID:cn-hangzhou}
  endpoint: ${ALIYUN_KMS_ENDPOINT:kms.cn-hangzhou.aliyuncs.com}
  group-id: ${ALIYUN_API_GATEWAY_GROUP_ID:your-group-id}

  kms:
    key-id: ${ALIYUN_KMS_KEY_ID:your-kms-key-id}
    key-version-id: ${ALIYUN_KMS_KEY_VERSION_ID:your-key-version-id}
    alg: ${ALIYUN_KMS_ALGORITHM:RSA_PKCS1_SHA_256}
```

## 环境变量配置

### 简单对称加密环境变量

```bash
# JWT密钥
JWT_SECRET_KEY=your-very-long-secret-key-for-hmac-sha256-encryption
```

### 阿里云KMS环境变量

```bash
# 阿里云访问凭证
ALIYUN_ACCESS_KEY_ID=your-access-key-id
ALIYUN_ACCESS_KEY_SECRET=your-access-key-secret
ALIYUN_REGION_ID=cn-hangzhou

# KMS配置
ALIYUN_KMS_ENDPOINT=kms.cn-hangzhou.aliyuncs.com
ALIYUN_KMS_KEY_ID=your-kms-key-id
ALIYUN_KMS_KEY_VERSION_ID=your-key-version-id
ALIYUN_KMS_ALGORITHM=RSA_PKCS1_SHA_256
```

## 使用示例

### 切换到简单对称加密

1. 修改 `application.yml`:
   ```yaml
   jwt:
     signer:
       type: simple
       simple:
         secret-key: my-super-secret-key-for-development
   ```

2. 重启应用

### 切换到阿里云KMS

1. 修改 `application.yml`:
   ```yaml
   jwt:
     signer:
       type: aliyun-kms
   ```

2. 确保阿里云相关配置正确

3. 重启应用

## 安全建议

### 简单对称加密
- 密钥长度至少32字符
- 使用随机生成的强密钥
- 定期轮换密钥
- 不要在代码中硬编码密钥

### 阿里云KMS
- 使用RAM用户而非主账号
- 遵循最小权限原则
- 定期轮换访问密钥
- 启用操作审计

## 测试验证

运行测试验证配置是否正确：

```bash
# 测试JWT签名服务
mvn test -Dtest=JwtSignerServiceTest

# 测试JWT访问令牌转换器
mvn test -Dtest=JwtAccessTokenConverterTest

# 运行所有测试
mvn test
```

## 故障排除

### 常见问题

1. **简单对称加密签名失败**
   - 检查密钥配置是否正确
   - 确保密钥长度足够

2. **阿里云KMS连接失败**
   - 检查网络连接
   - 验证访问凭证
   - 确认KMS密钥权限

3. **Bean注入失败**
   - 检查配置文件语法
   - 确认Spring Boot版本兼容性
