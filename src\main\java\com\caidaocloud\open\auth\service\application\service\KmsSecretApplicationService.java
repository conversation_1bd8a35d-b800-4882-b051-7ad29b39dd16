package com.caidaocloud.open.auth.service.application.service;

import com.aliyun.kms20160120.models.CreateSecretResponseBody;
import com.aliyun.kms20160120.models.GetSecretValueResponseBody;
import com.aliyun.kms20160120.models.PutSecretValueResponseBody;
import com.caidaocloud.open.auth.service.infrastructure.config.aliyun.KmsSecretManager;
import com.caidaocloud.open.auth.service.interfaces.dto.kms.*;
import com.caidaocloud.util.ObjectConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * KMS密钥管理应用服务
 * 
 * <AUTHOR>
 * @date 2025/9/5
 */
@Slf4j
@Service
public class KmsSecretApplicationService {

    @Autowired
    private KmsSecretManager kmsSecretManager;

    /**
     * 创建密钥
     * 
     * @param requestDto 创建密钥请求
     * @return 创建密钥响应
     */
    public CreateSecretResponseDto createSecret(CreateSecretRequestDto requestDto) {
        log.info("开始创建密钥，密钥名称: {}, 版本ID: {}", requestDto.getSecretName(), requestDto.getVersionId());
        
        try {
            CreateSecretResponseBody responseBody = kmsSecretManager.createSecret(
                requestDto.getSecretName(),
                requestDto.getVersionId(),
                requestDto.getSecretData()
            );
            
            CreateSecretResponseDto responseDto = ObjectConverter.convert(responseBody, CreateSecretResponseDto.class);
            
            log.info("密钥创建成功，密钥名称: {}, ARN: {}", requestDto.getSecretName(), responseBody.getArn());
            return responseDto;
        } catch (Exception e) {
            log.error("创建密钥失败，密钥名称: {}, 错误信息: {}", requestDto.getSecretName(), e.getMessage(), e);
            throw new RuntimeException("创建密钥失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取密钥值
     * 
     * @param requestDto 获取密钥值请求
     * @return 获取密钥值响应
     */
    public GetSecretValueResponseDto getSecretValue(GetSecretValueRequestDto requestDto) {
        log.info("开始获取密钥值，密钥名称: {}", requestDto.getSecretName());
        
        try {
            GetSecretValueResponseBody responseBody = kmsSecretManager.getSecretValue(requestDto.getSecretName());
            
            GetSecretValueResponseDto responseDto = ObjectConverter.convert(responseBody, GetSecretValueResponseDto.class);
            
            log.info("密钥值获取成功，密钥名称: {}", requestDto.getSecretName());
            return responseDto;
        } catch (Exception e) {
            log.error("获取密钥值失败，密钥名称: {}, 错误信息: {}", requestDto.getSecretName(), e.getMessage(), e);
            throw new RuntimeException("获取密钥值失败: " + e.getMessage(), e);
        }
    }

    /**
     * 更新密钥值
     * 
     * @param requestDto 更新密钥值请求
     * @return 更新密钥值响应
     */
    public PutSecretValueResponseDto putSecretValue(PutSecretValueRequestDto requestDto) {
        log.info("开始更新密钥值，密钥名称: {}, 版本ID: {}", requestDto.getSecretName(), requestDto.getVersionId());
        
        try {
            PutSecretValueResponseBody responseBody = kmsSecretManager.putSecretValue(
                requestDto.getVersionId(),
                requestDto.getSecretName(),
                requestDto.getSecretData()
            );
            
            PutSecretValueResponseDto responseDto = ObjectConverter.convert(responseBody, PutSecretValueResponseDto.class);
            
            log.info("密钥值更新成功，密钥名称: {}, 版本ID: {}", requestDto.getSecretName(), requestDto.getVersionId());
            return responseDto;
        } catch (Exception e) {
            log.error("更新密钥值失败，密钥名称: {}, 错误信息: {}", requestDto.getSecretName(), e.getMessage(), e);
            throw new RuntimeException("更新密钥值失败: " + e.getMessage(), e);
        }
    }
}
