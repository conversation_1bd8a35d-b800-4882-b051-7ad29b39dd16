package com.caidaocloud.open.auth.service.application.service;

import com.aliyun.kms20160120.models.CreateSecretResponseBody;
import com.aliyun.kms20160120.models.GetSecretValueResponseBody;
import com.aliyun.kms20160120.models.PutSecretValueResponseBody;
import com.caidaocloud.open.auth.service.infrastructure.config.aliyun.KmsSecretManager;
import com.caidaocloud.open.auth.service.interfaces.dto.kms.KmsSecretRequestDto;
import com.caidaocloud.open.auth.service.interfaces.dto.kms.KmsSecretResponseDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * KMS密钥管理应用服务
 * 
 * <AUTHOR>
 * @date 2025/9/5
 */
@Slf4j
@Service
public class KmsSecretApplicationService {

    @Autowired
    private KmsSecretManager kmsSecretManager;

    /**
     * 创建密钥
     * 
     * @param requestDto 创建密钥请求
     * @return 创建密钥响应
     */
    public KmsSecretResponseDto createSecret(KmsSecretRequestDto requestDto) {
        log.info("开始创建密钥，密钥名称: {}, 版本ID: {}", requestDto.getSecretName(), requestDto.getVersionId());

        try {
            CreateSecretResponseBody responseBody = kmsSecretManager.createSecret(
                    requestDto.getSecretName(),
                    requestDto.getVersionId(),
                    requestDto.getSecretData());

            log.info("密钥创建成功，密钥名称: {}", requestDto.getSecretName());
            return KmsSecretResponseDto.createSuccess(requestDto.getSecretName(), requestDto.getVersionId());
        } catch (Exception e) {
            log.error("创建密钥失败，密钥名称: {}, 错误信息: {}", requestDto.getSecretName(), e.getMessage(), e);
            return KmsSecretResponseDto.failure(requestDto.getSecretName(), "创建密钥失败: " + e.getMessage());
        }
    }

    /**
     * 获取密钥值
     * 
     * @param requestDto 获取密钥值请求
     * @return 获取密钥值响应
     */
    public KmsSecretResponseDto getSecretValue(KmsSecretRequestDto requestDto) {
        log.info("开始获取密钥值，密钥名称: {}", requestDto.getSecretName());

        try {
            GetSecretValueResponseBody responseBody = kmsSecretManager.getSecretValue(requestDto.getSecretName());

            log.info("密钥值获取成功，密钥名称: {}", requestDto.getSecretName());
            return KmsSecretResponseDto.getSuccess(
                    requestDto.getSecretName(),
                    responseBody.getVersionId(),
                    responseBody.getSecretData());
        } catch (Exception e) {
            log.error("获取密钥值失败，密钥名称: {}, 错误信息: {}", requestDto.getSecretName(), e.getMessage(), e);
            return KmsSecretResponseDto.failure(requestDto.getSecretName(), "获取密钥值失败: " + e.getMessage());
        }
    }

    /**
     * 更新密钥值
     * 
     * @param requestDto 更新密钥值请求
     * @return 更新密钥值响应
     */
    public KmsSecretResponseDto putSecretValue(KmsSecretRequestDto requestDto) {
        log.info("开始更新密钥值，密钥名称: {}, 版本ID: {}", requestDto.getSecretName(), requestDto.getVersionId());

        try {
            PutSecretValueResponseBody responseBody = kmsSecretManager.putSecretValue(
                    requestDto.getVersionId(),
                    requestDto.getSecretName(),
                    requestDto.getSecretData());

            log.info("密钥值更新成功，密钥名称: {}, 版本ID: {}", requestDto.getSecretName(), requestDto.getVersionId());
            return KmsSecretResponseDto.putSuccess(requestDto.getSecretName(), requestDto.getVersionId());
        } catch (Exception e) {
            log.error("更新密钥值失败，密钥名称: {}, 错误信息: {}", requestDto.getSecretName(), e.getMessage(), e);
            return KmsSecretResponseDto.failure(requestDto.getSecretName(), "更新密钥值失败: " + e.getMessage());
        }
    }
}
