
# JWT签名配置
jwt:
  signer:
    # 签名方式选择: simple(简单对称加密) 或 aliyun-kms(阿里云KMS)
    type: aliyun-kms
    # 简单对称加密配置

spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    url: *******************************************************************************************************
    username: postgres
    password: Caidao2022
    driver-class-name: org.postgresql.Driver
  hikari:
    connection-timeout: 60000
    maximum-pool-size: 50
    validation-timeout: 3000
    idle-timeount: 60000
    max-lifetime: 60000
    login-timeount: 5
  redis:
    host: ***************
    port: 6379
    password: myredis
    database: 1
    timeout: 6000
    pool:
      max-idle: 20
      min-idle: 10
      max-active: 50
      max-wait: 5000
      test-on-borrow: true
  application:
    name: caidao-open-auth-service
# Alibaba Cloud Configuration (当jwt.signer.type=aliyun-kms时需要配置)
aliyun:
  access-key-id: LTAI5tMopr9XJ8ZzseoreP22
  access-key-secret: ******************************
  endpoint: kms.cn-shanghai.aliyuncs.com
  # KMS Configuration
  kms:
    key-id: key-shh68b8189doykpwqiyrp
    key-version-id: key-shh68b8189doykpwqiyrp-mx38q825ph
    alg: RSA_PSS_SHA_256