package com.caidaocloud.util;

import java.security.NoSuchAlgorithmException;

import com.caidaocloud.open.auth.service.infrastructure.util.SHA256Util;
import org.junit.Assert;
import org.junit.Test;

import org.springframework.util.Base64Utils;

/**
 *
 * <AUTHOR>
 * @date 2025/9/4
 */
public class SHA256UtilTest {
	private static String target_digest = "uCx5YpLfBrqoYMP8Hf9H7j9/1zT+PPxq1qJRW6uQbos=";
	private static String source_str = "this is message";

	@Test
	public void encode_test() throws NoSuchAlgorithmException {
		Assert.assertEquals(target_digest, SHA256Util.base64Encode(source_str));
	}
}
