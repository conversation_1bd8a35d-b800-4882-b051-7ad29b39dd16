package com.caidaocloud.open.auth.service.domain.repository;

import com.caidaocloud.open.auth.service.domain.entity.OAuthClient;

import java.util.List;
import java.util.Optional;

/**
 * OAuth Client Repository Interface (Domain Layer)
 * 
 * <AUTHOR>
 */
public interface OAuthClientRepository {

    /**
     * Find client by client ID
     */
    Optional<OAuthClient> findByClientId(String clientId);

}
