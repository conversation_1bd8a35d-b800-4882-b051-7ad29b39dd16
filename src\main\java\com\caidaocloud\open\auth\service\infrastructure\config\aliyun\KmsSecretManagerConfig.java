package com.caidaocloud.open.auth.service.infrastructure.config.aliyun;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * KMS密钥管理器配置
 * 
 * <AUTHOR>
 * @date 2025/9/5
 */
@Configuration
public class KmsSecretManagerConfig {

    @Autowired
    private AliyunConfig aliyunConfig;

    /**
     * 创建KmsSecretManager Bean
     * 
     * @return KmsSecretManager实例
     * @throws Exception 配置异常
     */
    @Bean
    public KmsSecretManager kmsSecretManager() throws Exception {
        return new KmsSecretManager(aliyunConfig);
    }
}
