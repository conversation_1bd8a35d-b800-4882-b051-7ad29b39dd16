package com.caidaocloud.open.auth.service.infrastructure.persistence.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.caidaocloud.open.auth.service.infrastructure.persistence.po.OAuthClientPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Optional;

/**
 * OAuth Client Mapper (Infrastructure Layer)
 * 
 * <AUTHOR>
 */
@Mapper
public interface OAuthClientMapper extends BaseMapper<OAuthClientPo> {

    /**
     * Find client by client id
     */
    Optional<OAuthClientPo> findByClientId(@Param("clientId") String clientId);

    /**
     * Find all enabled clients
     */
    List<OAuthClientPo> findAllEnabled();

    /**
     * Check if client id exists
     */
    boolean existsByClientId(@Param("clientId") String clientId);

    /**
     * Update client last used time
     */
    void updateLastUsed(@Param("clientId") String clientId);

    /**
     * Update client status
     */
    void updateStatus(@Param("clientId") String clientId, @Param("enabled") boolean enabled);
}
