2025-09-03 18:53:15.591 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-09-03 18:53:15.602 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-09-03 18:53:16.018 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$ba70f495] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:53:16.762 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-09-03 18:53:16.762 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-09-03 18:53:16.762 [main] INFO  c.c.o.a.service.OpenAuthApplication - The following profiles are active: dev
2025-09-03 18:53:18.875 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-09-03 18:53:19.533 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-09-03 18:53:19.543 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-09-03 18:53:19.655 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 71ms. Found 0 repository interfaces.
2025-09-03 18:53:19.823 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-09-03 18:53:20.305 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=34d23542-6ddc-37d9-b900-624789f5721b
2025-09-03 18:53:20.350 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IConditionFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:53:20.352 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IDictFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:53:20.353 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IEntityDataFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:53:20.355 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IMetadataAuthFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:53:20.358 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IMetadataFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:53:20.360 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.transaction.configuration.feign.ITransactionFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:53:20.431 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.DistributedLockConfiguration' of type [com.jarvis.cache.autoconfigure.DistributedLockConfiguration$$EnhancerBySpringCGLIB$$61d0bc4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:53:20.453 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:53:20.459 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration$$EnhancerBySpringCGLIB$$b370afe2] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:53:20.579 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:53:20.657 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:53:20.663 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoLoadCacheDistributedLock' of type [com.jarvis.cache.redis.SpringRedisLock] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:53:20.676 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoload.cache-com.jarvis.cache.autoconfigure.AutoloadCacheProperties' of type [com.jarvis.cache.autoconfigure.AutoloadCacheProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:53:20.677 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure' of type [com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure$$EnhancerBySpringCGLIB$$4c5aea26] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:53:20.792 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$9e56f198] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:53:20.984 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration$$EnhancerBySpringCGLIB$$d397eb43] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:53:21.011 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$$EnhancerBySpringCGLIB$$d99aff80] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:53:21.028 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheSerializer' of type [com.jarvis.cache.serializer.JdkSerializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:53:21.281 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2025-09-03 18:53:21.282 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2025-09-03 18:53:21.940 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheCacheManager' of type [com.jarvis.cache.redis.SpringRedisCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:53:21.960 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheScriptParser' of type [com.jarvis.cache.script.SpringELParser] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:53:21.987 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheHandler' of type [com.jarvis.cache.CacheHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:53:21.997 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteTransactionalInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:53:22.002 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:53:22.005 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:53:22.006 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:53:22.009 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheMethodInterceptor' of type [com.jarvis.cache.interceptor.CacheMethodInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:53:22.010 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:53:22.038 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$ba70f495] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:53:22.532 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-09-03 18:53:22.545 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-09-03 18:53:22.550 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-09-03 18:53:22.550 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet Engine: Apache Tomcat/9.0.12
2025-09-03 18:53:22.556 [main] INFO  o.a.c.core.AprLifecycleListener - The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\bin\default;C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\bin;C:\windows\system32;C:\windows;C:\windows\system32;C:\windows;C:\windows\System32\Wbem;C:\windows\System32\WindowsPowerShell\v1.0\;C:\windows\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Users\<USER>\.jdks\temurin-1.8.0_302\bin;C:\Users\<USER>\.jdks\temurin-1.8.0_302\jre\bin;C:\Program Files\JetBrains\IntelliJ IDEA 2021.2.2\plugins\maven\lib\maven3\bin;C:\Program Files\Kubernetes\Minikube;;C:\minikube;C:\Program Files\Docker\Docker\resources\bin;C:\ProgramData\DockerDesktop\version-bin;C:\Program Files (x86)\Tencent\微信web开发者工具\dll;C:\Program Files\nodejs\;C:\Program Files\cursor\resources\app\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Users\<USER>\scoop\shims;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\IntelliJ IDEA 2021.2.2\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;.]
2025-09-03 18:53:22.761 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-09-03 18:53:22.761 [main] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 5976 ms
2025-09-03 18:53:24.490 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'characterEncodingFilter' to: [/*]
2025-09-03 18:53:24.491 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'webMvcMetricsFilter' to: [/*]
2025-09-03 18:53:24.491 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2025-09-03 18:53:24.491 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'formContentFilter' to: [/*]
2025-09-03 18:53:24.491 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'requestContextFilter' to: [/*]
2025-09-03 18:53:24.492 [main] INFO  o.s.b.w.s.DelegatingFilterProxyRegistrationBean - Mapping filter: 'springSecurityFilterChain' to: [/*]
2025-09-03 18:53:24.492 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'commonScopeFilter' to urls: [/*]
2025-09-03 18:53:24.492 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'httpTraceFilter' to: [/*]
2025-09-03 18:53:24.492 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'HTTPBasicAuthorizeAttribute' to urls: [/autoload-cache-ui.html, /autoload-cache/*]
2025-09-03 18:53:24.493 [main] INFO  o.s.b.w.s.ServletRegistrationBean - Servlet dispatcherServlet mapped to [/]
2025-09-03 18:53:24.592 [main] WARN  o.s.s.o.p.t.s.JwtAccessTokenConverter - Unable to create an RSA verifier from verifierKey (ignoreable if using MAC)
2025-09-03 18:53:25.557 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.caidaocloud.open.auth.service.infrastructure.persistence.po.OAuthClientPo".
2025-09-03 18:53:25.565 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.caidaocloud.open.auth.service.infrastructure.persistence.po.OAuthClientPo ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-09-03 18:53:26.535 [main] INFO  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: bb264b1a-92a0-4650-b7c9-d28703321c4c

2025-09-03 18:53:26.740 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Creating filter chain: OrRequestMatcher [requestMatchers=[Ant [pattern='/api/open/v1/oauth/token'], Ant [pattern='/api/open/v1/oauth/token_key'], Ant [pattern='/api/open/v1/oauth/check_token']]], [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@ac493b3e, org.springframework.security.web.context.SecurityContextPersistenceFilter@db4531b2, org.springframework.security.web.header.HeaderWriterFilter@73ab9951, org.springframework.security.web.authentication.logout.LogoutFilter@b2d7f7c8, org.springframework.security.oauth2.provider.client.ClientCredentialsTokenEndpointFilter@507bc0a2, com.caidaocloud.open.auth.service.infrastructure.config.oauth.OAuthClientCredentialsTokenEndpointFilter@772e1e0b, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@91dfd240, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@8f3b10b6, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7a7b5aa8, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@c6b6c357, org.springframework.security.web.session.SessionManagementFilter@2ea04113, org.springframework.security.web.access.ExceptionTranslationFilter@16f5ba1, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@b22674f9]
2025-09-03 18:53:26.745 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@9a6b9d80, org.springframework.security.web.context.SecurityContextPersistenceFilter@26ff7548, org.springframework.security.web.header.HeaderWriterFilter@5e670f97, org.springframework.security.web.authentication.logout.LogoutFilter@f6d88605, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@61b34bb, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@536739ba, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@c72860bb, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@b2ec065b, org.springframework.security.web.session.SessionManagementFilter@ae9cb9a, org.springframework.security.web.access.ExceptionTranslationFilter@111453ac, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@ed2ecf03]
2025-09-03 18:53:26.791 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-09-03 18:53:26.793 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-09-03 18:53:26.800 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-09-03 18:53:26.800 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-09-03 18:53:27.187 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2025-09-03 18:53:27.428 [main] INFO  o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService
2025-09-03 18:53:28.963 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-09-03 18:53:29.797 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-09-03 18:53:29.798 [main] INFO  o.a.tomcat.util.net.NioSelectorPool - Using a shared selector for servlet write/read
2025-09-03 18:53:29.817 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-09-03 18:53:29.859 [main] ERROR c.a.c.n.r.NacosServiceRegistry - nacos registry, caidao-open-auth-service register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='', endpoint='', namespace='', watchDelay=30000, logName='', service='caidao-open-auth-service', weight=1.0, clusterName='DEFAULT', namingLoadCacheAtStart='false', metadata={preserved.register.source=SPRING_CLOUD}, registerEnabled=true, ip='**************', networkInterface='', port=8080, secure=false, accessKey='', secretKey=''}},
com.alibaba.nacos.api.exception.NacosException: no server available
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:521)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:501)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:496)
	at com.alibaba.nacos.client.naming.net.NamingProxy.registerService(NamingProxy.java:249)
	at com.alibaba.nacos.client.naming.NacosNamingService.registerInstance(NacosNamingService.java:218)
	at com.alibaba.nacos.client.naming.NacosNamingService.registerInstance(NacosNamingService.java:207)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:61)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.register(AbstractAutoServiceRegistration.java:219)
	at com.alibaba.cloud.nacos.registry.NacosAutoServiceRegistration.register(NacosAutoServiceRegistration.java:74)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.start(AbstractAutoServiceRegistration.java:118)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.bind(AbstractAutoServiceRegistration.java:82)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.onApplicationEvent(AbstractAutoServiceRegistration.java:69)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.onApplicationEvent(AbstractAutoServiceRegistration.java:32)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:172)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:165)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:139)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:398)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:355)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.finishRefresh(ServletWebServerApplicationContext.java:164)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:549)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:140)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:775)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1260)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1248)
	at com.caidaocloud.open.auth.service.OpenAuthApplication.main(OpenAuthApplication.java:17)
2025-09-03 18:53:29.864 [main] INFO  c.c.o.a.service.OpenAuthApplication - Started OpenAuthApplication in 15.234 seconds (JVM running for 16.225)
2025-09-03 18:53:48.600 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-03 18:53:48.601 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-09-03 18:53:48.677 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 75 ms
2025-09-03 18:53:49.259 [http-nio-8080-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-09-03 18:53:49.534 [http-nio-8080-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-09-03 18:53:50.403 [http-nio-8080-exec-1] WARN  o.s.s.o.p.endpoint.TokenEndpoint - Handling error: ServerException, Identity information is missing in accesstoken
2025-09-03 18:54:17.559 [http-nio-8080-exec-3] WARN  o.s.s.o.p.endpoint.TokenEndpoint - Handling error: ServerException, Identity information is missing in accesstoken
2025-09-03 18:54:24.987 [Thread-32] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-09-03 18:54:24.989 [Thread-32] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-09-03 18:54:24.989 [Thread-26] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-09-03 18:54:24.991 [Thread-26] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-09-03 18:54:25.002 [Thread-37] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-09-03 18:54:25.004 [Thread-37] ERROR c.a.c.n.r.NacosServiceRegistry - ERR_NACOS_DEREGISTER, de-register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='', endpoint='', namespace='', watchDelay=30000, logName='', service='caidao-open-auth-service', weight=1.0, clusterName='DEFAULT', namingLoadCacheAtStart='false', metadata={preserved.register.source=SPRING_CLOUD}, registerEnabled=true, ip='**************', networkInterface='', port=8080, secure=false, accessKey='', secretKey=''}},
com.alibaba.nacos.api.exception.NacosException: no server available
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:521)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:501)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:496)
	at com.alibaba.nacos.client.naming.net.NamingProxy.deregisterService(NamingProxy.java:274)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:258)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:244)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:233)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:85)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.deregister(AbstractAutoServiceRegistration.java:236)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.stop(AbstractAutoServiceRegistration.java:251)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.destroy(AbstractAutoServiceRegistration.java:188)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:363)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:321)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:156)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:240)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:571)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:543)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1052)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:504)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1059)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1035)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1011)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:931)
2025-09-03 18:54:25.005 [Thread-37] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-09-03 18:54:25.007 [Thread-37] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'applicationTaskExecutor'
2025-09-03 18:54:25.019 [Thread-37] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2025-09-03 18:54:26.149 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - RSA_PSS_SHA_256 was destroying!
2025-09-03 18:54:26.149 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - key-shh68b8189doykpwqiyrp was destroying!
2025-09-03 18:54:26.150 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - false was destroying!
2025-09-03 18:54:26.150 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - key-shh68b8189doykpwqiyrp-mx38q825ph was destroying!
2025-09-03 18:54:26.150 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - class com.alibaba.nacos.spring.context.annotation.config.NacosValueAnnotationBeanPostProcessor was destroying!
2025-09-03 18:54:26.151 [Thread-37] INFO  c.a.n.s.b.f.a.AnnotationNacosInjectedBeanPostProcessor - class com.alibaba.nacos.spring.beans.factory.annotation.AnnotationNacosInjectedBeanPostProcessor was destroying!
2025-09-03 18:54:26.152 [Thread-37] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-09-03 18:54:26.154 [Thread-37] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-09-03 18:54:35.663 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-09-03 18:54:35.668 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-09-03 18:54:36.473 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$2682a2bb] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:54:37.615 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-09-03 18:54:37.615 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-09-03 18:54:37.616 [main] INFO  c.c.o.a.service.OpenAuthApplication - The following profiles are active: dev
2025-09-03 18:54:43.364 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-09-03 18:54:44.387 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-09-03 18:54:44.396 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-09-03 18:54:44.548 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 106ms. Found 0 repository interfaces.
2025-09-03 18:54:44.868 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-09-03 18:54:45.984 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=34d23542-6ddc-37d9-b900-624789f5721b
2025-09-03 18:54:46.121 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IConditionFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:54:46.130 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IDictFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:54:46.137 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IEntityDataFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:54:46.146 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IMetadataAuthFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:54:46.153 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IMetadataFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:54:46.157 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.transaction.configuration.feign.ITransactionFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:54:46.366 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.DistributedLockConfiguration' of type [com.jarvis.cache.autoconfigure.DistributedLockConfiguration$$EnhancerBySpringCGLIB$$722eb9ea] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:54:46.412 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:54:46.441 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration$$EnhancerBySpringCGLIB$$1f825e08] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:54:46.734 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:54:46.951 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:54:46.973 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoLoadCacheDistributedLock' of type [com.jarvis.cache.redis.SpringRedisLock] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:54:47.013 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoload.cache-com.jarvis.cache.autoconfigure.AutoloadCacheProperties' of type [com.jarvis.cache.autoconfigure.AutoloadCacheProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:54:47.013 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure' of type [com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure$$EnhancerBySpringCGLIB$$b86c984c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:54:47.383 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$a689fbe] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:54:47.917 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration$$EnhancerBySpringCGLIB$$3fa99969] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:54:47.991 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$$EnhancerBySpringCGLIB$$45acada6] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:54:48.035 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheSerializer' of type [com.jarvis.cache.serializer.JdkSerializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:54:49.003 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2025-09-03 18:54:49.008 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2025-09-03 18:54:50.366 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheCacheManager' of type [com.jarvis.cache.redis.SpringRedisCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:54:50.447 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheScriptParser' of type [com.jarvis.cache.script.SpringELParser] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:54:50.516 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheHandler' of type [com.jarvis.cache.CacheHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:54:50.544 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteTransactionalInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:54:50.562 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:54:50.578 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:54:50.580 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:54:50.591 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheMethodInterceptor' of type [com.jarvis.cache.interceptor.CacheMethodInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:54:50.594 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:54:50.802 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$2682a2bb] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:54:52.868 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-09-03 18:54:52.906 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-09-03 18:54:52.931 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-09-03 18:54:52.932 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet Engine: Apache Tomcat/9.0.12
2025-09-03 18:54:52.949 [main] INFO  o.a.c.core.AprLifecycleListener - The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\bin\default;C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\bin;C:\windows\system32;C:\windows;C:\windows\system32;C:\windows;C:\windows\System32\Wbem;C:\windows\System32\WindowsPowerShell\v1.0\;C:\windows\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Users\<USER>\.jdks\temurin-1.8.0_302\bin;C:\Users\<USER>\.jdks\temurin-1.8.0_302\jre\bin;C:\Program Files\JetBrains\IntelliJ IDEA 2021.2.2\plugins\maven\lib\maven3\bin;C:\Program Files\Kubernetes\Minikube;;C:\minikube;C:\Program Files\Docker\Docker\resources\bin;C:\ProgramData\DockerDesktop\version-bin;C:\Program Files (x86)\Tencent\微信web开发者工具\dll;C:\Program Files\nodejs\;C:\Program Files\cursor\resources\app\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Users\<USER>\scoop\shims;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\IntelliJ IDEA 2021.2.2\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;.]
2025-09-03 18:54:53.541 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-09-03 18:54:53.542 [main] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 15829 ms
2025-09-03 18:54:58.753 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'characterEncodingFilter' to: [/*]
2025-09-03 18:54:58.755 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'webMvcMetricsFilter' to: [/*]
2025-09-03 18:54:58.756 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2025-09-03 18:54:58.756 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'formContentFilter' to: [/*]
2025-09-03 18:54:58.756 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'requestContextFilter' to: [/*]
2025-09-03 18:54:58.758 [main] INFO  o.s.b.w.s.DelegatingFilterProxyRegistrationBean - Mapping filter: 'springSecurityFilterChain' to: [/*]
2025-09-03 18:54:58.759 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'commonScopeFilter' to urls: [/*]
2025-09-03 18:54:58.760 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'httpTraceFilter' to: [/*]
2025-09-03 18:54:58.760 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'HTTPBasicAuthorizeAttribute' to urls: [/autoload-cache-ui.html, /autoload-cache/*]
2025-09-03 18:54:58.761 [main] INFO  o.s.b.w.s.ServletRegistrationBean - Servlet dispatcherServlet mapped to [/]
2025-09-03 18:54:59.018 [main] WARN  o.s.s.o.p.t.s.JwtAccessTokenConverter - Unable to create an RSA verifier from verifierKey (ignoreable if using MAC)
2025-09-03 18:55:01.332 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.caidaocloud.open.auth.service.infrastructure.persistence.po.OAuthClientPo".
2025-09-03 18:55:01.364 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.caidaocloud.open.auth.service.infrastructure.persistence.po.OAuthClientPo ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-09-03 18:55:04.408 [main] INFO  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 6570c5bf-47de-4e47-852a-691bdd4ca99e

2025-09-03 18:55:05.065 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Creating filter chain: OrRequestMatcher [requestMatchers=[Ant [pattern='/api/open/v1/oauth/token'], Ant [pattern='/api/open/v1/oauth/token_key'], Ant [pattern='/api/open/v1/oauth/check_token']]], [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7a408a71, org.springframework.security.web.context.SecurityContextPersistenceFilter@24d32456, org.springframework.security.web.header.HeaderWriterFilter@55ca1cc2, org.springframework.security.web.authentication.logout.LogoutFilter@c1785471, org.springframework.security.oauth2.provider.client.ClientCredentialsTokenEndpointFilter@669b7459, com.caidaocloud.open.auth.service.infrastructure.config.oauth.OAuthClientCredentialsTokenEndpointFilter@c5bac0b4, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@c6b2b393, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@93904f34, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@86908011, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4de7ddd9, org.springframework.security.web.session.SessionManagementFilter@ea45f0f3, org.springframework.security.web.access.ExceptionTranslationFilter@a757a965, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@c7c02955]
2025-09-03 18:55:05.084 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@a37c5d39, org.springframework.security.web.context.SecurityContextPersistenceFilter@42ec30, org.springframework.security.web.header.HeaderWriterFilter@ddd499c1, org.springframework.security.web.authentication.logout.LogoutFilter@a90ed27d, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@32368ede, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@b0ddf55, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@86eed9c2, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@b198d672, org.springframework.security.web.session.SessionManagementFilter@72b72cc2, org.springframework.security.web.access.ExceptionTranslationFilter@5467928e, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@482cd3f7]
2025-09-03 18:55:05.211 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-09-03 18:55:05.212 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-09-03 18:55:05.227 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-09-03 18:55:05.228 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-09-03 18:55:06.524 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2025-09-03 18:55:07.295 [main] INFO  o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService
2025-09-03 18:55:11.427 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-09-03 18:55:14.596 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-09-03 18:55:14.605 [main] INFO  o.a.tomcat.util.net.NioSelectorPool - Using a shared selector for servlet write/read
2025-09-03 18:55:14.655 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-09-03 18:55:14.782 [main] ERROR c.a.c.n.r.NacosServiceRegistry - nacos registry, caidao-open-auth-service register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='', endpoint='', namespace='', watchDelay=30000, logName='', service='caidao-open-auth-service', weight=1.0, clusterName='DEFAULT', namingLoadCacheAtStart='false', metadata={preserved.register.source=SPRING_CLOUD}, registerEnabled=true, ip='**************', networkInterface='', port=8080, secure=false, accessKey='', secretKey=''}},
com.alibaba.nacos.api.exception.NacosException: no server available
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:521)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:501)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:496)
	at com.alibaba.nacos.client.naming.net.NamingProxy.registerService(NamingProxy.java:249)
	at com.alibaba.nacos.client.naming.NacosNamingService.registerInstance(NacosNamingService.java:218)
	at com.alibaba.nacos.client.naming.NacosNamingService.registerInstance(NacosNamingService.java:207)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:61)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.register(AbstractAutoServiceRegistration.java:219)
	at com.alibaba.cloud.nacos.registry.NacosAutoServiceRegistration.register(NacosAutoServiceRegistration.java:74)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.start(AbstractAutoServiceRegistration.java:118)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.bind(AbstractAutoServiceRegistration.java:82)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.onApplicationEvent(AbstractAutoServiceRegistration.java:69)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.onApplicationEvent(AbstractAutoServiceRegistration.java:32)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:172)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:165)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:139)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:398)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:355)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.finishRefresh(ServletWebServerApplicationContext.java:164)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:549)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:140)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:775)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1260)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1248)
	at com.caidaocloud.open.auth.service.OpenAuthApplication.main(OpenAuthApplication.java:17)
2025-09-03 18:55:14.791 [main] INFO  c.c.o.a.service.OpenAuthApplication - Started OpenAuthApplication in 40.372 seconds (JVM running for 44.931)
2025-09-03 18:55:22.734 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-03 18:55:22.735 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-09-03 18:55:22.777 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 41 ms
2025-09-03 18:55:44.593 [http-nio-8080-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-09-03 18:55:45.381 [http-nio-8080-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-09-03 18:56:14.862 [http-nio-8080-exec-1] WARN  o.s.s.o.p.endpoint.TokenEndpoint - Handling error: ServerException, Identity information is missing in accesstoken
2025-09-03 18:58:39.396 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=48s789ms566µs).
2025-09-03 18:58:39.706 [http-nio-8080-exec-2] WARN  o.s.s.o.p.endpoint.TokenEndpoint - Handling error: ServerException, Identity information is missing in accesstoken
2025-09-03 18:58:39.910 [Thread-26] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-09-03 18:58:39.916 [Thread-32] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-09-03 18:58:39.919 [Thread-32] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-09-03 18:58:39.974 [Thread-26] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-09-03 18:58:40.211 [Thread-37] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-09-03 18:58:40.236 [Thread-37] ERROR c.a.c.n.r.NacosServiceRegistry - ERR_NACOS_DEREGISTER, de-register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='', endpoint='', namespace='', watchDelay=30000, logName='', service='caidao-open-auth-service', weight=1.0, clusterName='DEFAULT', namingLoadCacheAtStart='false', metadata={preserved.register.source=SPRING_CLOUD}, registerEnabled=true, ip='**************', networkInterface='', port=8080, secure=false, accessKey='', secretKey=''}},
com.alibaba.nacos.api.exception.NacosException: no server available
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:521)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:501)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:496)
	at com.alibaba.nacos.client.naming.net.NamingProxy.deregisterService(NamingProxy.java:274)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:258)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:244)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:233)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:85)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.deregister(AbstractAutoServiceRegistration.java:236)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.stop(AbstractAutoServiceRegistration.java:251)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.destroy(AbstractAutoServiceRegistration.java:188)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:363)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:321)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:156)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:240)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:571)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:543)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1052)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:504)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1059)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1035)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1011)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:931)
2025-09-03 18:58:40.239 [Thread-37] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-09-03 18:58:40.251 [Thread-37] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'applicationTaskExecutor'
2025-09-03 18:58:40.337 [Thread-37] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2025-09-03 18:58:41.745 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - RSA_PSS_SHA_256 was destroying!
2025-09-03 18:58:41.746 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - key-shh68b8189doykpwqiyrp was destroying!
2025-09-03 18:58:41.746 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - false was destroying!
2025-09-03 18:58:41.747 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - key-shh68b8189doykpwqiyrp-mx38q825ph was destroying!
2025-09-03 18:58:41.768 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - class com.alibaba.nacos.spring.context.annotation.config.NacosValueAnnotationBeanPostProcessor was destroying!
2025-09-03 18:58:41.783 [Thread-37] INFO  c.a.n.s.b.f.a.AnnotationNacosInjectedBeanPostProcessor - class com.alibaba.nacos.spring.beans.factory.annotation.AnnotationNacosInjectedBeanPostProcessor was destroying!
2025-09-03 18:58:41.792 [Thread-37] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-09-03 18:58:42.093 [Thread-37] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-09-03 18:59:05.661 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-09-03 18:59:05.672 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-09-03 18:59:06.425 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$29a5165e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:59:07.610 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-09-03 18:59:07.610 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-09-03 18:59:07.610 [main] INFO  c.c.o.a.service.OpenAuthApplication - The following profiles are active: dev
2025-09-03 18:59:09.134 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-09-03 18:59:09.642 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-09-03 18:59:09.646 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-09-03 18:59:09.730 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 58ms. Found 0 repository interfaces.
2025-09-03 18:59:09.890 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-09-03 18:59:10.519 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=34d23542-6ddc-37d9-b900-624789f5721b
2025-09-03 18:59:10.610 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IConditionFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:59:10.614 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IDictFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:59:10.618 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IEntityDataFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:59:10.623 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IMetadataAuthFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:59:10.626 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IMetadataFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:59:10.628 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.transaction.configuration.feign.ITransactionFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:59:10.736 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.DistributedLockConfiguration' of type [com.jarvis.cache.autoconfigure.DistributedLockConfiguration$$EnhancerBySpringCGLIB$$75512d8d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:59:10.761 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:59:10.779 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration$$EnhancerBySpringCGLIB$$22a4d1ab] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:59:10.950 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:59:11.068 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:59:11.081 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoLoadCacheDistributedLock' of type [com.jarvis.cache.redis.SpringRedisLock] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:59:11.111 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoload.cache-com.jarvis.cache.autoconfigure.AutoloadCacheProperties' of type [com.jarvis.cache.autoconfigure.AutoloadCacheProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:59:11.112 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure' of type [com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure$$EnhancerBySpringCGLIB$$bb8f0bef] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:59:11.349 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$d8b1361] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:59:11.663 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration$$EnhancerBySpringCGLIB$$42cc0d0c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:59:11.709 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$$EnhancerBySpringCGLIB$$48cf2149] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:59:11.736 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheSerializer' of type [com.jarvis.cache.serializer.JdkSerializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:59:12.150 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2025-09-03 18:59:12.152 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2025-09-03 18:59:13.570 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheCacheManager' of type [com.jarvis.cache.redis.SpringRedisCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:59:13.593 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheScriptParser' of type [com.jarvis.cache.script.SpringELParser] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:59:13.621 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheHandler' of type [com.jarvis.cache.CacheHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:59:13.633 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteTransactionalInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:59:13.639 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:59:13.643 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:59:13.643 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:59:13.647 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheMethodInterceptor' of type [com.jarvis.cache.interceptor.CacheMethodInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:59:13.649 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:59:13.683 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$29a5165e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 18:59:14.340 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-09-03 18:59:14.354 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-09-03 18:59:14.364 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-09-03 18:59:14.364 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet Engine: Apache Tomcat/9.0.12
2025-09-03 18:59:14.370 [main] INFO  o.a.c.core.AprLifecycleListener - The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\bin\default;C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\bin;C:\windows\system32;C:\windows;C:\windows\system32;C:\windows;C:\windows\System32\Wbem;C:\windows\System32\WindowsPowerShell\v1.0\;C:\windows\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Users\<USER>\.jdks\temurin-1.8.0_302\bin;C:\Users\<USER>\.jdks\temurin-1.8.0_302\jre\bin;C:\Program Files\JetBrains\IntelliJ IDEA 2021.2.2\plugins\maven\lib\maven3\bin;C:\Program Files\Kubernetes\Minikube;;C:\minikube;C:\Program Files\Docker\Docker\resources\bin;C:\ProgramData\DockerDesktop\version-bin;C:\Program Files (x86)\Tencent\微信web开发者工具\dll;C:\Program Files\nodejs\;C:\Program Files\cursor\resources\app\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Users\<USER>\scoop\shims;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\IntelliJ IDEA 2021.2.2\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;.]
2025-09-03 18:59:14.827 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-09-03 18:59:14.828 [main] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 7193 ms
2025-09-03 18:59:17.269 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'characterEncodingFilter' to: [/*]
2025-09-03 18:59:17.270 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'webMvcMetricsFilter' to: [/*]
2025-09-03 18:59:17.271 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2025-09-03 18:59:17.272 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'formContentFilter' to: [/*]
2025-09-03 18:59:17.272 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'requestContextFilter' to: [/*]
2025-09-03 18:59:17.273 [main] INFO  o.s.b.w.s.DelegatingFilterProxyRegistrationBean - Mapping filter: 'springSecurityFilterChain' to: [/*]
2025-09-03 18:59:17.275 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'commonScopeFilter' to urls: [/*]
2025-09-03 18:59:17.276 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'httpTraceFilter' to: [/*]
2025-09-03 18:59:17.276 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'HTTPBasicAuthorizeAttribute' to urls: [/autoload-cache-ui.html, /autoload-cache/*]
2025-09-03 18:59:17.276 [main] INFO  o.s.b.w.s.ServletRegistrationBean - Servlet dispatcherServlet mapped to [/]
2025-09-03 18:59:17.478 [main] WARN  o.s.s.o.p.t.s.JwtAccessTokenConverter - Unable to create an RSA verifier from verifierKey (ignoreable if using MAC)
2025-09-03 18:59:18.678 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.caidaocloud.open.auth.service.infrastructure.persistence.po.OAuthClientPo".
2025-09-03 18:59:18.690 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.caidaocloud.open.auth.service.infrastructure.persistence.po.OAuthClientPo ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-09-03 18:59:20.274 [main] INFO  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 9c3da438-7fe8-449a-a4f2-277fb4c08a0d

2025-09-03 18:59:20.507 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Creating filter chain: OrRequestMatcher [requestMatchers=[Ant [pattern='/api/open/v1/oauth/token'], Ant [pattern='/api/open/v1/oauth/token_key'], Ant [pattern='/api/open/v1/oauth/check_token']]], [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@71b3c381, org.springframework.security.web.context.SecurityContextPersistenceFilter@b77cbde2, org.springframework.security.web.header.HeaderWriterFilter@73416e57, org.springframework.security.web.authentication.logout.LogoutFilter@296aee10, org.springframework.security.oauth2.provider.client.ClientCredentialsTokenEndpointFilter@aae2f565, com.caidaocloud.open.auth.service.infrastructure.config.oauth.OAuthClientCredentialsTokenEndpointFilter@c64ca7d7, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@c5d8b90c, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@530ed742, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@fe045536, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@da0e6d00, org.springframework.security.web.session.SessionManagementFilter@9284b72e, org.springframework.security.web.access.ExceptionTranslationFilter@dfb9764b, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@422745ef]
2025-09-03 18:59:20.521 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1521cde7, org.springframework.security.web.context.SecurityContextPersistenceFilter@b0052d6b, org.springframework.security.web.header.HeaderWriterFilter@b0b66397, org.springframework.security.web.authentication.logout.LogoutFilter@be7489d4, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@a50850ab, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@d0e50070, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@ca0bd84a, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@295f5952, org.springframework.security.web.session.SessionManagementFilter@31c96c38, org.springframework.security.web.access.ExceptionTranslationFilter@ab3f5780, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@3159e9b]
2025-09-03 18:59:20.585 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-09-03 18:59:20.586 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-09-03 18:59:20.594 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-09-03 18:59:20.594 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-09-03 18:59:21.119 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2025-09-03 18:59:21.426 [main] INFO  o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService
2025-09-03 18:59:23.395 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-09-03 18:59:24.430 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-09-03 18:59:24.432 [main] INFO  o.a.tomcat.util.net.NioSelectorPool - Using a shared selector for servlet write/read
2025-09-03 18:59:24.459 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-09-03 18:59:24.543 [main] ERROR c.a.c.n.r.NacosServiceRegistry - nacos registry, caidao-open-auth-service register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='', endpoint='', namespace='', watchDelay=30000, logName='', service='caidao-open-auth-service', weight=1.0, clusterName='DEFAULT', namingLoadCacheAtStart='false', metadata={preserved.register.source=SPRING_CLOUD}, registerEnabled=true, ip='**************', networkInterface='', port=8080, secure=false, accessKey='', secretKey=''}},
com.alibaba.nacos.api.exception.NacosException: no server available
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:521)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:501)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:496)
	at com.alibaba.nacos.client.naming.net.NamingProxy.registerService(NamingProxy.java:249)
	at com.alibaba.nacos.client.naming.NacosNamingService.registerInstance(NacosNamingService.java:218)
	at com.alibaba.nacos.client.naming.NacosNamingService.registerInstance(NacosNamingService.java:207)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:61)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.register(AbstractAutoServiceRegistration.java:219)
	at com.alibaba.cloud.nacos.registry.NacosAutoServiceRegistration.register(NacosAutoServiceRegistration.java:74)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.start(AbstractAutoServiceRegistration.java:118)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.bind(AbstractAutoServiceRegistration.java:82)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.onApplicationEvent(AbstractAutoServiceRegistration.java:69)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.onApplicationEvent(AbstractAutoServiceRegistration.java:32)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:172)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:165)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:139)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:398)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:355)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.finishRefresh(ServletWebServerApplicationContext.java:164)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:549)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:140)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:775)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1260)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1248)
	at com.caidaocloud.open.auth.service.OpenAuthApplication.main(OpenAuthApplication.java:17)
2025-09-03 18:59:24.549 [main] INFO  c.c.o.a.service.OpenAuthApplication - Started OpenAuthApplication in 20.79 seconds (JVM running for 22.471)
2025-09-03 18:59:37.382 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-03 18:59:37.384 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-09-03 18:59:37.453 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 68 ms
2025-09-03 18:59:37.840 [http-nio-8080-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-09-03 18:59:38.220 [http-nio-8080-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-09-03 19:00:21.544 [http-nio-8080-exec-1] ERROR c.c.o.a.s.i.c.a.AliyunKmsJwtAccessTokenConverter - Failed to encode JWT with Aliyun KMS
com.aliyun.tea.TeaException: code: 400, The specified parameter Digest is not valid. request id: 916866b8-1540-47fc-b567-ef1813c9da8a
	at com.aliyun.teaopenapi.Client.doRequest(Client.java:956)
	at com.aliyun.teaopenapi.Client.callApi(Client.java:1207)
	at com.aliyun.kms20160120.Client.asymmetricSignWithOptions(Client.java:407)
	at com.caidaocloud.open.auth.service.infrastructure.config.aliyun.KmsJwtSigner.sign(KmsJwtSigner.java:55)
	at com.caidaocloud.open.auth.service.infrastructure.config.aliyun.KmsJwtSigner.sign(KmsJwtSigner.java:113)
	at com.caidaocloud.open.auth.service.infrastructure.config.aliyun.AliyunKmsJwtAccessTokenConverter.encode(AliyunKmsJwtAccessTokenConverter.java:48)
	at org.springframework.security.oauth2.provider.token.store.JwtAccessTokenConverter.enhance(JwtAccessTokenConverter.java:226)
	at org.springframework.security.oauth2.provider.token.TokenEnhancerChain.enhance(TokenEnhancerChain.java:47)
	at org.springframework.security.oauth2.provider.token.DefaultTokenServices.createAccessToken(DefaultTokenServices.java:301)
	at org.springframework.security.oauth2.provider.token.DefaultTokenServices.createAccessToken(DefaultTokenServices.java:121)
	at org.springframework.security.oauth2.provider.token.DefaultTokenServices$$FastClassBySpringCGLIB$$5a1f25c.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:746)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at <unknown class>.proceedWithInvocation(Unknown Source)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:294)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:98)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	at org.springframework.security.oauth2.provider.token.DefaultTokenServices$$EnhancerBySpringCGLIB$$e391c897.createAccessToken(<generated>)
	at org.springframework.security.oauth2.provider.token.DefaultTokenServices$$FastClassBySpringCGLIB$$5a1f25c.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:746)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at <unknown class>.proceedWithInvocation(Unknown Source)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:294)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:98)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	at org.springframework.security.oauth2.provider.token.DefaultTokenServices$$EnhancerBySpringCGLIB$$1394139d.createAccessToken(<generated>)
	at org.springframework.security.oauth2.provider.token.AbstractTokenGranter.getAccessToken(AbstractTokenGranter.java:72)
	at org.springframework.security.oauth2.provider.token.AbstractTokenGranter.grant(AbstractTokenGranter.java:67)
	at org.springframework.security.oauth2.provider.client.ClientCredentialsTokenGranter.grant(ClientCredentialsTokenGranter.java:52)
	at org.springframework.security.oauth2.provider.CompositeTokenGranter.grant(CompositeTokenGranter.java:38)
	at org.springframework.security.oauth2.provider.endpoint.TokenEndpoint.postAccessToken(TokenEndpoint.java:132)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:215)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:142)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:102)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:800)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1038)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:942)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:998)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:901)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:660)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:875)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:90)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.caidaocloud.security.filter.AuthScopeFilter.doFilter(AuthScopeFilter.java:29)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:320)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:127)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:91)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:137)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:111)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:170)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.www.BasicAuthenticationFilter.doFilterInternal(BasicAuthenticationFilter.java:158)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.oauth2.provider.client.ClientCredentialsTokenEndpointFilter.successfulAuthentication(ClientCredentialsTokenEndpointFilter.java:131)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:240)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.oauth2.provider.client.ClientCredentialsTokenEndpointFilter.successfulAuthentication(ClientCredentialsTokenEndpointFilter.java:131)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:240)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:66)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:56)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:215)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:357)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:270)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:154)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:122)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:107)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:199)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:770)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1415)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:826)
2025-09-03 19:12:53.497 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=12m31s918ms572µs600ns).
2025-09-03 19:12:53.853 [http-nio-8080-exec-1] WARN  o.s.s.o.p.endpoint.TokenEndpoint - Handling error: RuntimeException, JWT encoding failed
2025-09-03 19:12:54.024 [Thread-32] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-09-03 19:12:54.025 [Thread-26] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-09-03 19:12:54.030 [Thread-32] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-09-03 19:12:54.047 [Thread-26] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-09-03 19:12:54.571 [Thread-37] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-09-03 19:12:54.639 [Thread-37] ERROR c.a.c.n.r.NacosServiceRegistry - ERR_NACOS_DEREGISTER, de-register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='', endpoint='', namespace='', watchDelay=30000, logName='', service='caidao-open-auth-service', weight=1.0, clusterName='DEFAULT', namingLoadCacheAtStart='false', metadata={preserved.register.source=SPRING_CLOUD}, registerEnabled=true, ip='**************', networkInterface='', port=8080, secure=false, accessKey='', secretKey=''}},
com.alibaba.nacos.api.exception.NacosException: no server available
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:521)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:501)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:496)
	at com.alibaba.nacos.client.naming.net.NamingProxy.deregisterService(NamingProxy.java:274)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:258)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:244)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:233)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:85)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.deregister(AbstractAutoServiceRegistration.java:236)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.stop(AbstractAutoServiceRegistration.java:251)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.destroy(AbstractAutoServiceRegistration.java:188)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:363)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:321)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:156)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:240)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:571)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:543)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1052)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:504)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1059)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1035)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1011)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:931)
2025-09-03 19:12:54.646 [Thread-37] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-09-03 19:12:54.672 [Thread-37] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'applicationTaskExecutor'
2025-09-03 19:12:54.807 [Thread-37] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2025-09-03 19:12:54.857 [http-nio-8080-exec-1] WARN  o.s.h.c.j.MappingJackson2HttpMessageConverter - Failed to evaluate Jackson serialization for type [class org.springframework.security.oauth2.provider.error.DefaultWebResponseExceptionTranslator$ServerErrorException]: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.security.oauth2.common.exceptions.OAuth2ExceptionJackson2Serializer': Initialization of bean failed; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'autoloadCacheAdvisor': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-09-03 19:12:55.182 [http-nio-8080-exec-1] WARN  o.s.h.c.j.MappingJackson2HttpMessageConverter - Failed to evaluate Jackson serialization for type [class org.springframework.security.oauth2.provider.error.DefaultWebResponseExceptionTranslator$ServerErrorException]: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.security.oauth2.common.exceptions.OAuth2ExceptionJackson2Serializer': BeanPostProcessor before instantiation of bean failed; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'autoloadCacheAdvisor': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-09-03 19:12:56.833 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - RSA_PSS_SHA_256 was destroying!
2025-09-03 19:12:56.836 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - key-shh68b8189doykpwqiyrp was destroying!
2025-09-03 19:12:56.837 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - false was destroying!
2025-09-03 19:12:56.844 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - key-shh68b8189doykpwqiyrp-mx38q825ph was destroying!
2025-09-03 19:12:56.902 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - class com.alibaba.nacos.spring.context.annotation.config.NacosValueAnnotationBeanPostProcessor was destroying!
2025-09-03 19:12:56.914 [Thread-37] INFO  c.a.n.s.b.f.a.AnnotationNacosInjectedBeanPostProcessor - class com.alibaba.nacos.spring.beans.factory.annotation.AnnotationNacosInjectedBeanPostProcessor was destroying!
2025-09-03 19:12:56.923 [Thread-37] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-09-03 19:12:56.973 [Thread-37] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-09-03 19:13:18.707 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-09-03 19:13:18.721 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-09-03 19:13:19.711 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$815826ca] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:13:21.144 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-09-03 19:13:21.145 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-09-03 19:13:21.146 [main] INFO  c.c.o.a.service.OpenAuthApplication - The following profiles are active: dev
2025-09-03 19:13:27.637 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-09-03 19:13:29.816 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-09-03 19:13:29.823 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-09-03 19:13:30.230 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 254ms. Found 0 repository interfaces.
2025-09-03 19:13:31.000 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-09-03 19:13:32.969 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=34d23542-6ddc-37d9-b900-624789f5721b
2025-09-03 19:13:33.177 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IConditionFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:13:33.185 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IDictFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:13:33.203 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IEntityDataFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:13:33.207 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IMetadataAuthFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:13:33.210 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IMetadataFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:13:33.212 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.transaction.configuration.feign.ITransactionFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:13:33.996 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.DistributedLockConfiguration' of type [com.jarvis.cache.autoconfigure.DistributedLockConfiguration$$EnhancerBySpringCGLIB$$cd043df9] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:13:34.160 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:13:34.180 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration$$EnhancerBySpringCGLIB$$7a57e217] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:13:35.195 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:13:35.561 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:13:35.614 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoLoadCacheDistributedLock' of type [com.jarvis.cache.redis.SpringRedisLock] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:13:35.690 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoload.cache-com.jarvis.cache.autoconfigure.AutoloadCacheProperties' of type [com.jarvis.cache.autoconfigure.AutoloadCacheProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:13:35.692 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure' of type [com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure$$EnhancerBySpringCGLIB$$13421c5b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:13:36.195 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$653e23cd] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:13:36.836 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration$$EnhancerBySpringCGLIB$$9a7f1d78] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:13:36.921 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$$EnhancerBySpringCGLIB$$a08231b5] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:13:36.997 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheSerializer' of type [com.jarvis.cache.serializer.JdkSerializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:13:38.081 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2025-09-03 19:13:38.086 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2025-09-03 19:13:39.566 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheCacheManager' of type [com.jarvis.cache.redis.SpringRedisCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:13:39.657 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheScriptParser' of type [com.jarvis.cache.script.SpringELParser] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:13:39.715 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheHandler' of type [com.jarvis.cache.CacheHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:13:39.742 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteTransactionalInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:13:39.758 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:13:39.772 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:13:39.773 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:13:39.782 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheMethodInterceptor' of type [com.jarvis.cache.interceptor.CacheMethodInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:13:39.785 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:13:39.852 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$815826ca] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:13:41.005 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-09-03 19:13:41.050 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-09-03 19:13:41.071 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-09-03 19:13:41.072 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet Engine: Apache Tomcat/9.0.12
2025-09-03 19:13:41.086 [main] INFO  o.a.c.core.AprLifecycleListener - The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\bin\default;C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\bin;C:\windows\system32;C:\windows;C:\windows\system32;C:\windows;C:\windows\System32\Wbem;C:\windows\System32\WindowsPowerShell\v1.0\;C:\windows\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Users\<USER>\.jdks\temurin-1.8.0_302\bin;C:\Users\<USER>\.jdks\temurin-1.8.0_302\jre\bin;C:\Program Files\JetBrains\IntelliJ IDEA 2021.2.2\plugins\maven\lib\maven3\bin;C:\Program Files\Kubernetes\Minikube;;C:\minikube;C:\Program Files\Docker\Docker\resources\bin;C:\ProgramData\DockerDesktop\version-bin;C:\Program Files (x86)\Tencent\微信web开发者工具\dll;C:\Program Files\nodejs\;C:\Program Files\cursor\resources\app\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Users\<USER>\scoop\shims;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\IntelliJ IDEA 2021.2.2\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;.]
2025-09-03 19:13:41.939 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-09-03 19:13:41.941 [main] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 20754 ms
2025-09-03 19:13:47.141 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'characterEncodingFilter' to: [/*]
2025-09-03 19:13:47.143 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'webMvcMetricsFilter' to: [/*]
2025-09-03 19:13:47.144 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2025-09-03 19:13:47.144 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'formContentFilter' to: [/*]
2025-09-03 19:13:47.145 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'requestContextFilter' to: [/*]
2025-09-03 19:13:47.147 [main] INFO  o.s.b.w.s.DelegatingFilterProxyRegistrationBean - Mapping filter: 'springSecurityFilterChain' to: [/*]
2025-09-03 19:13:47.148 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'commonScopeFilter' to urls: [/*]
2025-09-03 19:13:47.148 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'httpTraceFilter' to: [/*]
2025-09-03 19:13:47.148 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'HTTPBasicAuthorizeAttribute' to urls: [/autoload-cache-ui.html, /autoload-cache/*]
2025-09-03 19:13:47.149 [main] INFO  o.s.b.w.s.ServletRegistrationBean - Servlet dispatcherServlet mapped to [/]
2025-09-03 19:13:47.377 [main] WARN  o.s.s.o.p.t.s.JwtAccessTokenConverter - Unable to create an RSA verifier from verifierKey (ignoreable if using MAC)
2025-09-03 19:13:48.673 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.caidaocloud.open.auth.service.infrastructure.persistence.po.OAuthClientPo".
2025-09-03 19:13:48.687 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.caidaocloud.open.auth.service.infrastructure.persistence.po.OAuthClientPo ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-09-03 19:13:50.520 [main] INFO  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: c87d46f1-e6ba-44d1-b74d-2e6aa45071be

2025-09-03 19:13:50.869 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Creating filter chain: OrRequestMatcher [requestMatchers=[Ant [pattern='/api/open/v1/oauth/token'], Ant [pattern='/api/open/v1/oauth/token_key'], Ant [pattern='/api/open/v1/oauth/check_token']]], [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@a28d65fa, org.springframework.security.web.context.SecurityContextPersistenceFilter@2bffff38, org.springframework.security.web.header.HeaderWriterFilter@710537dd, org.springframework.security.web.authentication.logout.LogoutFilter@43a29ab3, org.springframework.security.oauth2.provider.client.ClientCredentialsTokenEndpointFilter@33b81088, com.caidaocloud.open.auth.service.infrastructure.config.oauth.OAuthClientCredentialsTokenEndpointFilter@31268e1d, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@b72006df, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@d14386a, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@d26eaaa, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@761b1081, org.springframework.security.web.session.SessionManagementFilter@30284947, org.springframework.security.web.access.ExceptionTranslationFilter@c8ebf920, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@44a69d6d]
2025-09-03 19:13:50.881 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@c090286f, org.springframework.security.web.context.SecurityContextPersistenceFilter@70306ce2, org.springframework.security.web.header.HeaderWriterFilter@89244990, org.springframework.security.web.authentication.logout.LogoutFilter@f227009b, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@c60003e9, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@8d8ff15, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@76cdaa32, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1d0f61fd, org.springframework.security.web.session.SessionManagementFilter@d466ae67, org.springframework.security.web.access.ExceptionTranslationFilter@f1409fb0, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@72b9abae]
2025-09-03 19:13:50.971 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-09-03 19:13:50.971 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-09-03 19:13:50.982 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-09-03 19:13:50.982 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-09-03 19:13:51.782 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2025-09-03 19:13:52.251 [main] INFO  o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService
2025-09-03 19:13:57.062 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-09-03 19:13:58.657 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-09-03 19:13:58.661 [main] INFO  o.a.tomcat.util.net.NioSelectorPool - Using a shared selector for servlet write/read
2025-09-03 19:13:58.699 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-09-03 19:13:58.841 [main] ERROR c.a.c.n.r.NacosServiceRegistry - nacos registry, caidao-open-auth-service register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='', endpoint='', namespace='', watchDelay=30000, logName='', service='caidao-open-auth-service', weight=1.0, clusterName='DEFAULT', namingLoadCacheAtStart='false', metadata={preserved.register.source=SPRING_CLOUD}, registerEnabled=true, ip='**************', networkInterface='', port=8080, secure=false, accessKey='', secretKey=''}},
com.alibaba.nacos.api.exception.NacosException: no server available
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:521)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:501)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:496)
	at com.alibaba.nacos.client.naming.net.NamingProxy.registerService(NamingProxy.java:249)
	at com.alibaba.nacos.client.naming.NacosNamingService.registerInstance(NacosNamingService.java:218)
	at com.alibaba.nacos.client.naming.NacosNamingService.registerInstance(NacosNamingService.java:207)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:61)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.register(AbstractAutoServiceRegistration.java:219)
	at com.alibaba.cloud.nacos.registry.NacosAutoServiceRegistration.register(NacosAutoServiceRegistration.java:74)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.start(AbstractAutoServiceRegistration.java:118)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.bind(AbstractAutoServiceRegistration.java:82)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.onApplicationEvent(AbstractAutoServiceRegistration.java:69)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.onApplicationEvent(AbstractAutoServiceRegistration.java:32)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:172)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:165)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:139)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:398)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:355)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.finishRefresh(ServletWebServerApplicationContext.java:164)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:549)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:140)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:775)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1260)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1248)
	at com.caidaocloud.open.auth.service.OpenAuthApplication.main(OpenAuthApplication.java:17)
2025-09-03 19:13:58.847 [main] INFO  c.c.o.a.service.OpenAuthApplication - Started OpenAuthApplication in 42.194 seconds (JVM running for 44.769)
2025-09-03 19:14:32.250 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-03 19:14:32.251 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-09-03 19:14:32.357 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 105 ms
2025-09-03 19:14:32.662 [http-nio-8080-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-09-03 19:14:33.134 [http-nio-8080-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-09-03 19:17:36.983 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=2m30s690ms571µs900ns).
2025-09-03 19:17:37.471 [Thread-32] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-09-03 19:17:37.478 [Thread-32] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-09-03 19:17:37.480 [Thread-26] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-09-03 19:17:37.474 [http-nio-8080-exec-1] ERROR c.c.o.a.s.i.c.a.AliyunKmsJwtAccessTokenConverter - Failed to encode JWT with Aliyun KMS
com.aliyun.tea.TeaException: code: 400, The specified parameter Digest is not valid. request id: 8e13764a-bf85-487b-879c-c9501ae62d3e
	at com.aliyun.teaopenapi.Client.doRequest(Client.java:956)
	at com.aliyun.teaopenapi.Client.callApi(Client.java:1207)
	at com.aliyun.kms20160120.Client.asymmetricSignWithOptions(Client.java:407)
	at com.caidaocloud.open.auth.service.infrastructure.config.aliyun.KmsJwtSigner.sign(KmsJwtSigner.java:56)
	at com.caidaocloud.open.auth.service.infrastructure.config.aliyun.KmsJwtSigner.sign(KmsJwtSigner.java:114)
	at com.caidaocloud.open.auth.service.infrastructure.config.aliyun.AliyunKmsJwtAccessTokenConverter.encode(AliyunKmsJwtAccessTokenConverter.java:48)
	at org.springframework.security.oauth2.provider.token.store.JwtAccessTokenConverter.enhance(JwtAccessTokenConverter.java:226)
	at org.springframework.security.oauth2.provider.token.TokenEnhancerChain.enhance(TokenEnhancerChain.java:47)
	at org.springframework.security.oauth2.provider.token.DefaultTokenServices.createAccessToken(DefaultTokenServices.java:301)
	at org.springframework.security.oauth2.provider.token.DefaultTokenServices.createAccessToken(DefaultTokenServices.java:121)
	at org.springframework.security.oauth2.provider.token.DefaultTokenServices$$FastClassBySpringCGLIB$$5a1f25c.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:746)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at <unknown class>.proceedWithInvocation(Unknown Source)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:294)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:98)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	at org.springframework.security.oauth2.provider.token.DefaultTokenServices$$EnhancerBySpringCGLIB$$4133e3a3.createAccessToken(<generated>)
	at org.springframework.security.oauth2.provider.token.DefaultTokenServices$$FastClassBySpringCGLIB$$5a1f25c.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:746)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at <unknown class>.proceedWithInvocation(Unknown Source)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:294)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:98)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	at org.springframework.security.oauth2.provider.token.DefaultTokenServices$$EnhancerBySpringCGLIB$$71362ea9.createAccessToken(<generated>)
	at org.springframework.security.oauth2.provider.token.AbstractTokenGranter.getAccessToken(AbstractTokenGranter.java:72)
	at org.springframework.security.oauth2.provider.token.AbstractTokenGranter.grant(AbstractTokenGranter.java:67)
	at org.springframework.security.oauth2.provider.client.ClientCredentialsTokenGranter.grant(ClientCredentialsTokenGranter.java:52)
	at org.springframework.security.oauth2.provider.CompositeTokenGranter.grant(CompositeTokenGranter.java:38)
	at org.springframework.security.oauth2.provider.endpoint.TokenEndpoint.postAccessToken(TokenEndpoint.java:132)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:215)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:142)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:102)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:800)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1038)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:942)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:998)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:901)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:660)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:875)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:90)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.caidaocloud.security.filter.AuthScopeFilter.doFilter(AuthScopeFilter.java:29)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:320)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:127)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:91)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:137)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:111)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:170)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.www.BasicAuthenticationFilter.doFilterInternal(BasicAuthenticationFilter.java:158)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.oauth2.provider.client.ClientCredentialsTokenEndpointFilter.successfulAuthentication(ClientCredentialsTokenEndpointFilter.java:131)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:240)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.oauth2.provider.client.ClientCredentialsTokenEndpointFilter.successfulAuthentication(ClientCredentialsTokenEndpointFilter.java:131)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:240)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:66)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:56)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:215)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:357)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:270)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:154)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:122)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:107)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:199)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:770)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1415)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:826)
2025-09-03 19:17:37.536 [Thread-26] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-09-03 19:17:37.927 [http-nio-8080-exec-1] WARN  o.s.s.o.p.endpoint.TokenEndpoint - Handling error: RuntimeException, JWT encoding failed
2025-09-03 19:17:38.517 [Thread-37] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-09-03 19:17:38.539 [Thread-37] ERROR c.a.c.n.r.NacosServiceRegistry - ERR_NACOS_DEREGISTER, de-register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='', endpoint='', namespace='', watchDelay=30000, logName='', service='caidao-open-auth-service', weight=1.0, clusterName='DEFAULT', namingLoadCacheAtStart='false', metadata={preserved.register.source=SPRING_CLOUD}, registerEnabled=true, ip='**************', networkInterface='', port=8080, secure=false, accessKey='', secretKey=''}},
com.alibaba.nacos.api.exception.NacosException: no server available
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:521)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:501)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:496)
	at com.alibaba.nacos.client.naming.net.NamingProxy.deregisterService(NamingProxy.java:274)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:258)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:244)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:233)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister(NacosServiceRegistry.java:85)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.deregister(AbstractAutoServiceRegistration.java:236)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.stop(AbstractAutoServiceRegistration.java:251)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.destroy(AbstractAutoServiceRegistration.java:188)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:363)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:321)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:156)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:240)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:571)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:543)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1052)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:504)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1059)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1035)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1011)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:931)
2025-09-03 19:17:38.543 [Thread-37] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-09-03 19:17:38.558 [Thread-37] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'applicationTaskExecutor'
2025-09-03 19:17:38.664 [Thread-37] INFO  com.jarvis.cache.AutoLoadHandler - ----------------------AutoLoadHandler.shutdown--------------------
2025-09-03 19:17:38.883 [http-nio-8080-exec-1] WARN  o.s.h.c.j.MappingJackson2HttpMessageConverter - Failed to evaluate Jackson serialization for type [class org.springframework.security.oauth2.provider.error.DefaultWebResponseExceptionTranslator$ServerErrorException]: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.security.oauth2.common.exceptions.OAuth2ExceptionJackson2Serializer': Initialization of bean failed; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'autoloadCacheAdvisor': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-09-03 19:17:38.888 [http-nio-8080-exec-1] WARN  o.s.h.c.j.MappingJackson2HttpMessageConverter - Failed to evaluate Jackson serialization for type [class org.springframework.security.oauth2.provider.error.DefaultWebResponseExceptionTranslator$ServerErrorException]: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.security.oauth2.common.exceptions.OAuth2ExceptionJackson2Serializer': BeanPostProcessor before instantiation of bean failed; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'autoloadCacheAdvisor': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-09-03 19:17:40.611 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - RSA_PSS_SHA_256 was destroying!
2025-09-03 19:17:40.612 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - key-shh68b8189doykpwqiyrp was destroying!
2025-09-03 19:17:40.632 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - false was destroying!
2025-09-03 19:17:40.632 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - key-shh68b8189doykpwqiyrp-mx38q825ph was destroying!
2025-09-03 19:17:41.228 [Thread-37] INFO  c.a.n.s.c.a.c.NacosValueAnnotationBeanPostProcessor - class com.alibaba.nacos.spring.context.annotation.config.NacosValueAnnotationBeanPostProcessor was destroying!
2025-09-03 19:17:41.295 [Thread-37] INFO  c.a.n.s.b.f.a.AnnotationNacosInjectedBeanPostProcessor - class com.alibaba.nacos.spring.beans.factory.annotation.AnnotationNacosInjectedBeanPostProcessor was destroying!
2025-09-03 19:17:41.296 [Thread-37] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-09-03 19:17:41.454 [Thread-37] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-09-03 19:18:00.463 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-09-03 19:18:00.472 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-09-03 19:18:01.436 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$fbd837ff] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:18:03.173 [main] INFO  c.a.b.n.c.u.NacosConfigPropertiesUtils - nacosConfigProperties : NacosConfigProperties{serverAddr='127.0.0.1:8848', contextPath='null', encode='null', endpoint='null', namespace='null', accessKey='null', secretKey='null', ramRoleName='null', autoRefresh=false, dataId='null', dataIds='null', group='DEFAULT_GROUP', type=null, maxRetry='null', configLongPollTimeout='null', configRetryTime='null', enableRemoteSyncConfig=false, extConfig=[], bootstrap=Bootstrap{enable=false, logEnable=false}}
2025-09-03 19:18:03.175 [main] INFO  c.a.b.n.c.a.NacosConfigApplicationContextInitializer - [Nacos Config Boot] : The preload configuration is not enabled
2025-09-03 19:18:03.176 [main] INFO  c.c.o.a.service.OpenAuthApplication - The following profiles are active: dev
2025-09-03 19:18:05.687 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-09-03 19:18:06.314 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-09-03 19:18:06.318 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-09-03 19:18:06.409 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 67ms. Found 0 repository interfaces.
2025-09-03 19:18:06.639 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-09-03 19:18:07.370 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=34d23542-6ddc-37d9-b900-624789f5721b
2025-09-03 19:18:07.461 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IConditionFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:18:07.468 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IDictFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:18:07.474 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IEntityDataFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:18:07.481 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IMetadataAuthFeignClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:18:07.483 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.configuration.feign.IMetadataFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:18:07.486 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.caidaocloud.hrpaas.metadata.sdk.transaction.configuration.feign.ITransactionFeign' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:18:07.622 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.DistributedLockConfiguration' of type [com.jarvis.cache.autoconfigure.DistributedLockConfiguration$$EnhancerBySpringCGLIB$$47844f2e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:18:07.662 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:18:07.679 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration$$EnhancerBySpringCGLIB$$f4d7f34c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:18:07.901 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:18:08.075 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:18:08.087 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoLoadCacheDistributedLock' of type [com.jarvis.cache.redis.SpringRedisLock] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:18:08.121 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoload.cache-com.jarvis.cache.autoconfigure.AutoloadCacheProperties' of type [com.jarvis.cache.autoconfigure.AutoloadCacheProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:18:08.123 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure' of type [com.jarvis.cache.autoconfigure.AutoloadCacheAutoConfigure$$EnhancerBySpringCGLIB$$8dc22d90] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:18:08.389 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$dfbe3502] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:18:08.766 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$LettuceCacheCacheManagerConfiguration$$EnhancerBySpringCGLIB$$14ff2ead] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:18:08.817 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration' of type [com.jarvis.cache.autoconfigure.AutoloadCacheManageConfiguration$$EnhancerBySpringCGLIB$$1b0242ea] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:18:08.853 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheSerializer' of type [com.jarvis.cache.serializer.JdkSerializer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:18:09.881 [main] INFO  io.lettuce.core.EpollProvider - Starting without optional epoll library
2025-09-03 19:18:09.884 [main] INFO  io.lettuce.core.KqueueProvider - Starting without optional kqueue library
2025-09-03 19:18:11.389 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheCacheManager' of type [com.jarvis.cache.redis.SpringRedisCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:18:11.430 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheScriptParser' of type [com.jarvis.cache.script.SpringELParser] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:18:11.473 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheHandler' of type [com.jarvis.cache.CacheHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:18:11.488 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteTransactionalInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:18:11.500 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteTransactionalAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:18:11.507 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteInterceptor' of type [com.jarvis.cache.interceptor.CacheDeleteInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:18:11.509 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheDeleteAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:18:11.517 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheMethodInterceptor' of type [com.jarvis.cache.interceptor.CacheMethodInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:18:11.522 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'autoloadCacheAdvisor' of type [com.jarvis.cache.autoconfigure.MethodAnnotationPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:18:11.567 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$fbd837ff] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-03 19:18:12.587 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-09-03 19:18:12.615 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-09-03 19:18:12.641 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-09-03 19:18:12.647 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet Engine: Apache Tomcat/9.0.12
2025-09-03 19:18:12.659 [main] INFO  o.a.c.core.AprLifecycleListener - The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\bin\default;C:\Users\<USER>\.jdks\semeru-1.8.0_312\jre\bin;C:\windows\system32;C:\windows;C:\windows\system32;C:\windows;C:\windows\System32\Wbem;C:\windows\System32\WindowsPowerShell\v1.0\;C:\windows\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Users\<USER>\.jdks\temurin-1.8.0_302\bin;C:\Users\<USER>\.jdks\temurin-1.8.0_302\jre\bin;C:\Program Files\JetBrains\IntelliJ IDEA 2021.2.2\plugins\maven\lib\maven3\bin;C:\Program Files\Kubernetes\Minikube;;C:\minikube;C:\Program Files\Docker\Docker\resources\bin;C:\ProgramData\DockerDesktop\version-bin;C:\Program Files (x86)\Tencent\微信web开发者工具\dll;C:\Program Files\nodejs\;C:\Program Files\cursor\resources\app\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Users\<USER>\scoop\shims;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\IntelliJ IDEA 2021.2.2\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;.]
2025-09-03 19:18:13.032 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-09-03 19:18:13.032 [main] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 9812 ms
2025-09-03 19:18:18.183 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'characterEncodingFilter' to: [/*]
2025-09-03 19:18:18.184 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'webMvcMetricsFilter' to: [/*]
2025-09-03 19:18:18.185 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2025-09-03 19:18:18.186 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'formContentFilter' to: [/*]
2025-09-03 19:18:18.186 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'requestContextFilter' to: [/*]
2025-09-03 19:18:18.188 [main] INFO  o.s.b.w.s.DelegatingFilterProxyRegistrationBean - Mapping filter: 'springSecurityFilterChain' to: [/*]
2025-09-03 19:18:18.190 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'commonScopeFilter' to urls: [/*]
2025-09-03 19:18:18.190 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'httpTraceFilter' to: [/*]
2025-09-03 19:18:18.191 [main] INFO  o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'HTTPBasicAuthorizeAttribute' to urls: [/autoload-cache-ui.html, /autoload-cache/*]
2025-09-03 19:18:18.191 [main] INFO  o.s.b.w.s.ServletRegistrationBean - Servlet dispatcherServlet mapped to [/]
2025-09-03 19:18:18.638 [main] WARN  o.s.s.o.p.t.s.JwtAccessTokenConverter - Unable to create an RSA verifier from verifierKey (ignoreable if using MAC)
2025-09-03 19:18:20.921 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.caidaocloud.open.auth.service.infrastructure.persistence.po.OAuthClientPo".
2025-09-03 19:18:21.017 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.caidaocloud.open.auth.service.infrastructure.persistence.po.OAuthClientPo ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-09-03 19:18:23.109 [main] INFO  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 91f7a0a8-48c5-4511-a344-db32c7516b1d

2025-09-03 19:18:23.454 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Creating filter chain: OrRequestMatcher [requestMatchers=[Ant [pattern='/api/open/v1/oauth/token'], Ant [pattern='/api/open/v1/oauth/token_key'], Ant [pattern='/api/open/v1/oauth/check_token']]], [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@75b6d291, org.springframework.security.web.context.SecurityContextPersistenceFilter@1e611ea7, org.springframework.security.web.header.HeaderWriterFilter@4cf2ff4, org.springframework.security.web.authentication.logout.LogoutFilter@d519014b, org.springframework.security.oauth2.provider.client.ClientCredentialsTokenEndpointFilter@73f30fc8, com.caidaocloud.open.auth.service.infrastructure.config.oauth.OAuthClientCredentialsTokenEndpointFilter@8f3ff08e, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@6c50b5d1, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@47a302f8, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@e220df11, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@b9b24df7, org.springframework.security.web.session.SessionManagementFilter@684f7457, org.springframework.security.web.access.ExceptionTranslationFilter@b4f562a7, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@b5e35318]
2025-09-03 19:18:23.463 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@bd72f45c, org.springframework.security.web.context.SecurityContextPersistenceFilter@bc62d6c5, org.springframework.security.web.header.HeaderWriterFilter@e0f8332, org.springframework.security.web.authentication.logout.LogoutFilter@fa34f674, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@ec6ebdf4, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@73411271, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@1d156b1e, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@e9e03896, org.springframework.security.web.session.SessionManagementFilter@14c31e43, org.springframework.security.web.access.ExceptionTranslationFilter@68d447b5, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@26baec64]
2025-09-03 19:18:23.540 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-09-03 19:18:23.541 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-09-03 19:18:23.549 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-09-03 19:18:23.549 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-09-03 19:18:24.299 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2025-09-03 19:18:24.879 [main] INFO  o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService
2025-09-03 19:18:27.758 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-09-03 19:18:29.212 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-09-03 19:18:29.219 [main] INFO  o.a.tomcat.util.net.NioSelectorPool - Using a shared selector for servlet write/read
2025-09-03 19:18:29.263 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-09-03 19:18:29.324 [main] ERROR c.a.c.n.r.NacosServiceRegistry - nacos registry, caidao-open-auth-service register failed...NacosRegistration{nacosDiscoveryProperties=NacosDiscoveryProperties{serverAddr='', endpoint='', namespace='', watchDelay=30000, logName='', service='caidao-open-auth-service', weight=1.0, clusterName='DEFAULT', namingLoadCacheAtStart='false', metadata={preserved.register.source=SPRING_CLOUD}, registerEnabled=true, ip='**************', networkInterface='', port=8080, secure=false, accessKey='', secretKey=''}},
com.alibaba.nacos.api.exception.NacosException: no server available
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:521)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:501)
	at com.alibaba.nacos.client.naming.net.NamingProxy.reqApi(NamingProxy.java:496)
	at com.alibaba.nacos.client.naming.net.NamingProxy.registerService(NamingProxy.java:249)
	at com.alibaba.nacos.client.naming.NacosNamingService.registerInstance(NacosNamingService.java:218)
	at com.alibaba.nacos.client.naming.NacosNamingService.registerInstance(NacosNamingService.java:207)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register(NacosServiceRegistry.java:61)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.register(AbstractAutoServiceRegistration.java:219)
	at com.alibaba.cloud.nacos.registry.NacosAutoServiceRegistration.register(NacosAutoServiceRegistration.java:74)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.start(AbstractAutoServiceRegistration.java:118)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.bind(AbstractAutoServiceRegistration.java:82)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.onApplicationEvent(AbstractAutoServiceRegistration.java:69)
	at org.springframework.cloud.client.serviceregistry.AbstractAutoServiceRegistration.onApplicationEvent(AbstractAutoServiceRegistration.java:32)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:172)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:165)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:139)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:398)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:355)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.finishRefresh(ServletWebServerApplicationContext.java:164)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:549)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:140)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:775)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:316)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1260)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1248)
	at com.caidaocloud.open.auth.service.OpenAuthApplication.main(OpenAuthApplication.java:17)
2025-09-03 19:18:29.329 [main] INFO  c.c.o.a.service.OpenAuthApplication - Started OpenAuthApplication in 31.477 seconds (JVM running for 33.938)
2025-09-03 19:18:35.664 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-03 19:18:35.666 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-09-03 19:18:35.706 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 39 ms
2025-09-03 19:18:35.961 [http-nio-8080-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-09-03 19:18:36.196 [http-nio-8080-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-09-03 19:18:52.680 [http-nio-8080-exec-1] ERROR c.c.o.a.s.i.c.a.AliyunKmsJwtAccessTokenConverter - Failed to encode JWT with Aliyun KMS
com.aliyun.tea.TeaException: code: 400, The specified parameter Digest is not valid. request id: 38ecc9c0-f1c4-42de-aeba-5d723bbec745
	at com.aliyun.teaopenapi.Client.doRequest(Client.java:956)
	at com.aliyun.teaopenapi.Client.callApi(Client.java:1207)
	at com.aliyun.kms20160120.Client.asymmetricSignWithOptions(Client.java:407)
	at com.caidaocloud.open.auth.service.infrastructure.config.aliyun.KmsJwtSigner.sign(KmsJwtSigner.java:56)
	at com.caidaocloud.open.auth.service.infrastructure.config.aliyun.KmsJwtSigner.sign(KmsJwtSigner.java:114)
	at com.caidaocloud.open.auth.service.infrastructure.config.aliyun.AliyunKmsJwtAccessTokenConverter.encode(AliyunKmsJwtAccessTokenConverter.java:48)
	at org.springframework.security.oauth2.provider.token.store.JwtAccessTokenConverter.enhance(JwtAccessTokenConverter.java:226)
	at org.springframework.security.oauth2.provider.token.TokenEnhancerChain.enhance(TokenEnhancerChain.java:47)
	at org.springframework.security.oauth2.provider.token.DefaultTokenServices.createAccessToken(DefaultTokenServices.java:301)
	at org.springframework.security.oauth2.provider.token.DefaultTokenServices.createAccessToken(DefaultTokenServices.java:121)
	at org.springframework.security.oauth2.provider.token.DefaultTokenServices$$FastClassBySpringCGLIB$$5a1f25c.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:746)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at <unknown class>.proceedWithInvocation(Unknown Source)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:294)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:98)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	at org.springframework.security.oauth2.provider.token.DefaultTokenServices$$EnhancerBySpringCGLIB$$ba5c3ca2.createAccessToken(<generated>)
	at org.springframework.security.oauth2.provider.token.DefaultTokenServices$$FastClassBySpringCGLIB$$5a1f25c.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:746)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at <unknown class>.proceedWithInvocation(Unknown Source)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:294)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:98)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	at org.springframework.security.oauth2.provider.token.DefaultTokenServices$$EnhancerBySpringCGLIB$$ea5e87a8.createAccessToken(<generated>)
	at org.springframework.security.oauth2.provider.token.AbstractTokenGranter.getAccessToken(AbstractTokenGranter.java:72)
	at org.springframework.security.oauth2.provider.token.AbstractTokenGranter.grant(AbstractTokenGranter.java:67)
	at org.springframework.security.oauth2.provider.client.ClientCredentialsTokenGranter.grant(ClientCredentialsTokenGranter.java:52)
	at org.springframework.security.oauth2.provider.CompositeTokenGranter.grant(CompositeTokenGranter.java:38)
	at org.springframework.security.oauth2.provider.endpoint.TokenEndpoint.postAccessToken(TokenEndpoint.java:132)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:215)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:142)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:102)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:800)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1038)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:942)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:998)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:901)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:660)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:875)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:90)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.caidaocloud.security.filter.AuthScopeFilter.doFilter(AuthScopeFilter.java:29)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:320)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:127)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:91)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:137)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:111)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:170)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.www.BasicAuthenticationFilter.doFilterInternal(BasicAuthenticationFilter.java:158)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.oauth2.provider.client.ClientCredentialsTokenEndpointFilter.successfulAuthentication(ClientCredentialsTokenEndpointFilter.java:131)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:240)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.oauth2.provider.client.ClientCredentialsTokenEndpointFilter.successfulAuthentication(ClientCredentialsTokenEndpointFilter.java:131)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:240)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:66)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:56)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:334)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:215)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:357)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:270)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:154)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:122)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:107)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:199)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:770)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1415)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:826)
2025-09-03 19:18:54.871 [http-nio-8080-exec-1] WARN  o.s.s.o.p.endpoint.TokenEndpoint - Handling error: RuntimeException, JWT encoding failed
