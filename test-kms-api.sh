#!/bin/bash

# KMS密钥管理API测试脚本
# 使用方法: ./test-kms-api.sh [base_url]
# 默认base_url为 http://localhost:8080

BASE_URL=${1:-"http://localhost:8080"}
API_BASE="$BASE_URL/api/open/v1/kms/secret"

echo "=== KMS密钥管理API测试 ==="
echo "Base URL: $BASE_URL"
echo ""

# 测试1: 创建密钥
echo "1. 测试创建密钥..."
CREATE_RESPONSE=$(curl -s -X POST "$API_BASE/create" \
  -H "Content-Type: application/json" \
  -d '{
    "secretName": "test-secret-001",
    "versionId": "v1",
    "secretData": "my-secret-data-123"
  }')

echo "创建密钥响应: $CREATE_RESPONSE"
echo ""

# 测试2: 获取密钥值
echo "2. 测试获取密钥值..."
GET_RESPONSE=$(curl -s -X POST "$API_BASE/getValue" \
  -H "Content-Type: application/json" \
  -d '{
    "secretName": "test-secret-001"
  }')

echo "获取密钥值响应: $GET_RESPONSE"
echo ""

# 测试3: 更新密钥值
echo "3. 测试更新密钥值..."
PUT_RESPONSE=$(curl -s -X POST "$API_BASE/putValue" \
  -H "Content-Type: application/json" \
  -d '{
    "versionId": "v2",
    "secretName": "test-secret-001",
    "secretData": "updated-secret-data-456"
  }')

echo "更新密钥值响应: $PUT_RESPONSE"
echo ""

# 测试4: 参数校验失败
echo "4. 测试参数校验（空密钥名称）..."
VALIDATION_RESPONSE=$(curl -s -X POST "$API_BASE/create" \
  -H "Content-Type: application/json" \
  -d '{
    "secretName": "",
    "versionId": "v1",
    "secretData": "test-data"
  }')

echo "参数校验响应: $VALIDATION_RESPONSE"
echo ""

# 测试5: 参数校验失败（缺少版本ID）
echo "5. 测试参数校验（缺少版本ID）..."
MISSING_VERSION_RESPONSE=$(curl -s -X POST "$API_BASE/create" \
  -H "Content-Type: application/json" \
  -d '{
    "secretName": "test-secret",
    "secretData": "test-data"
  }')

echo "缺少版本ID响应: $MISSING_VERSION_RESPONSE"
echo ""

# 测试6: 参数校验失败（缺少密钥数据）
echo "6. 测试参数校验（缺少密钥数据）..."
MISSING_DATA_RESPONSE=$(curl -s -X POST "$API_BASE/create" \
  -H "Content-Type: application/json" \
  -d '{
    "secretName": "test-secret",
    "versionId": "v1"
  }')

echo "缺少密钥数据响应: $MISSING_DATA_RESPONSE"
echo ""

echo "=== 测试完成 ==="
