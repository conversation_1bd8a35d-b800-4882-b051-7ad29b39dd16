package com.caidaocloud.open.auth.service.infrastructure.config.aliyun.request;

import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.Map;

import com.aliyun.kms20160120.Client;
import com.aliyun.tea.TeaException;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.open.auth.service.infrastructure.config.aliyun.KmsOperationException;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 通用KMS请求包装类
 */
@Slf4j
public abstract class KmsRequestWrapper<REQ, RESP> {

    private final Client kmsClient;
    private final String methodName;

    public KmsRequestWrapper(Client kmsClient, String methodName) {
        this.kmsClient = kmsClient;
        this.methodName = methodName;
    }

    /**
     * 执行KMS操作
     */
    @SuppressWarnings("unchecked")
    public RESP execute(REQ request)  {
        try {
            Method method = kmsClient.getClass().getMethod(methodName, request.getClass());
            log.debug("执行KMS操作: {},request:{}", methodName,request);
            RESP response = (RESP) method.invoke(kmsClient, request);
            log.debug("KMS操作成功: {},request:{}", methodName, request);
            return response;
        } catch (TeaException error) {
            log.error("KMS操作失败: {}, 错误信息: {}", methodName, error.getMessage());
            if (error.getData() != null && error.getData().get("Recommend") != null) {
                log.error("诊断建议: {}", error.getData().get("Recommend"));
            }
            throw new KmsOperationException("KMS操作失败: " + methodName, error);
        } catch (Exception error) {
            log.error("KMS操作异常: {}, 错误信息: {}", methodName, error.getMessage());
            throw new KmsOperationException("KMS操作异常: " + methodName, error);
        }
    }

    public RequestBuilder<REQ> builder(Class<REQ> reqClass){
        return new RequestBuilder<>(reqClass);
    }

    public RequestBuilder<REQ> builder(){
        return new RequestBuilder<>(genericClass());
    }

    public Class<REQ> genericClass() {
        // 获取当前类的泛型超类
        Type superClass = getClass().getGenericSuperclass();

        // 如果是参数化类型
        if (superClass instanceof ParameterizedType) {
            ParameterizedType parameterizedType = (ParameterizedType) superClass;
            // 获取泛型参数
            Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
            if (actualTypeArguments.length > 0) {
                // 返回第一个泛型参数的 Class
                return (Class<REQ>) actualTypeArguments[0];
            }
        }

        // 如果无法获取泛型类型，返回 null 或抛出异常
        throw new RuntimeException("Unable to determine generic type");
    }


    // 通用请求Builder
    public static class RequestBuilder<REQ> {
        private final Class<REQ> request;
        private final Map<String, Object> parameters;

        public RequestBuilder(Class<REQ> requestClass) {
            try {
                this.request = requestClass;
                this.parameters = new HashMap<>();
            } catch (Exception e) {
                throw new ServerException("Failed to instantiate request: " + e.getMessage(), e);
            }
        }

        public RequestBuilder<REQ> setParameter(String key, Object value) {
            parameters.put(key, value);
            return this;
        }

        public REQ build() {
            return FastjsonUtil.convertObject(parameters, request);
        }
    }
}