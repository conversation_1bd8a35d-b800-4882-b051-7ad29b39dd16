package com.caidaocloud.open.auth.service.interfaces.facade;

import com.caidaocloud.open.auth.service.application.service.KmsSecretApplicationService;
import com.caidaocloud.open.auth.service.interfaces.dto.kms.*;
import com.caidaocloud.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * KMS密钥管理控制器
 * 
 * <AUTHOR>
 * @date 2025/9/5
 */
@Slf4j
@RestController
@RequestMapping("/api/open/v1/kms/secret")
public class KmsSecretController {

    @Autowired
    private KmsSecretApplicationService kmsSecretApplicationService;

    /**
     * 创建密钥
     * 
     * @param requestDto 创建密钥请求
     * @return 创建密钥响应
     */
    @PostMapping("/create")
    public Result<CreateSecretResponseDto> createSecret(@Valid @RequestBody CreateSecretRequestDto requestDto) {
        log.info("接收到创建密钥请求，密钥名称: {}", requestDto.getSecretName());

        try {
            CreateSecretResponseDto responseDto = kmsSecretApplicationService.createSecret(requestDto);
            return Result.ok(responseDto);
        } catch (Exception e) {
            log.error("创建密钥接口调用失败: {}", e.getMessage(), e);
            return Result.fail("创建密钥失败: " + e.getMessage());
        }
    }

    /**
     * 获取密钥值
     * 
     * @param requestDto 获取密钥值请求
     * @return 获取密钥值响应
     */
    @PostMapping("/getValue")
    public Result<GetSecretValueResponseDto> getSecretValue(@Valid @RequestBody GetSecretValueRequestDto requestDto) {
        log.info("接收到获取密钥值请求，密钥名称: {}", requestDto.getSecretName());

        try {
            GetSecretValueResponseDto responseDto = kmsSecretApplicationService.getSecretValue(requestDto);
            return Result.ok(responseDto);
        } catch (Exception e) {
            log.error("获取密钥值接口调用失败: {}", e.getMessage(), e);
            return Result.fail("获取密钥值失败: " + e.getMessage());
        }
    }

    /**
     * 更新密钥值
     * 
     * @param requestDto 更新密钥值请求
     * @return 更新密钥值响应
     */
    @PostMapping("/putValue")
    public Result<PutSecretValueResponseDto> putSecretValue(@Valid @RequestBody PutSecretValueRequestDto requestDto) {
        log.info("接收到更新密钥值请求，密钥名称: {}", requestDto.getSecretName());

        try {
            PutSecretValueResponseDto responseDto = kmsSecretApplicationService.putSecretValue(requestDto);
            return Result.ok(responseDto);
        } catch (Exception e) {
            log.error("更新密钥值接口调用失败: {}", e.getMessage(), e);
            return Result.fail("更新密钥值失败: " + e.getMessage());
        }
    }
}
