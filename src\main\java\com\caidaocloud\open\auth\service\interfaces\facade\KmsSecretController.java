package com.caidaocloud.open.auth.service.interfaces.facade;

import com.caidaocloud.open.auth.service.application.service.KmsSecretApplicationService;
import com.caidaocloud.open.auth.service.interfaces.dto.kms.KmsSecretRequestDto;
import com.caidaocloud.open.auth.service.interfaces.dto.kms.KmsSecretResponseDto;
import com.caidaocloud.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * KMS密钥管理控制器
 * 
 * <AUTHOR>
 * @date 2025/9/5
 */
@Slf4j
@RestController
@RequestMapping("/api/open/v1/kms/secret")
public class KmsSecretController {

    @Autowired
    private KmsSecretApplicationService kmsSecretApplicationService;

    /**
     * 创建密钥
     *
     * @param requestDto 创建密钥请求
     * @return 创建密钥响应
     */
    @PostMapping("/create")
    public Result<KmsSecretResponseDto> createSecret(@Valid @RequestBody KmsSecretRequestDto requestDto) {
        log.info("接收到创建密钥请求，密钥名称: {}", requestDto.getSecretName());

        try {
            // 验证创建密钥的必填参数
            requestDto.validateForCreate();

            KmsSecretResponseDto responseDto = kmsSecretApplicationService.createSecret(requestDto);
            return Result.ok(responseDto);
        } catch (IllegalArgumentException e) {
            log.warn("创建密钥参数校验失败: {}", e.getMessage());
            return Result.fail("参数校验失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("创建密钥接口调用失败: {}", e.getMessage(), e);
            return Result.fail("创建密钥失败: " + e.getMessage());
        }
    }

    /**
     * 获取密钥值
     *
     * @param requestDto 获取密钥值请求
     * @return 获取密钥值响应
     */
    @PostMapping("/getValue")
    public Result<KmsSecretResponseDto> getSecretValue(@Valid @RequestBody KmsSecretRequestDto requestDto) {
        log.info("接收到获取密钥值请求，密钥名称: {}", requestDto.getSecretName());

        try {
            KmsSecretResponseDto responseDto = kmsSecretApplicationService.getSecretValue(requestDto);
            return Result.ok(responseDto);
        } catch (Exception e) {
            log.error("获取密钥值接口调用失败: {}", e.getMessage(), e);
            return Result.fail("获取密钥值失败: " + e.getMessage());
        }
    }

    /**
     * 更新密钥值
     *
     * @param requestDto 更新密钥值请求
     * @return 更新密钥值响应
     */
    @PostMapping("/putValue")
    public Result<KmsSecretResponseDto> putSecretValue(@Valid @RequestBody KmsSecretRequestDto requestDto) {
        log.info("接收到更新密钥值请求，密钥名称: {}", requestDto.getSecretName());

        try {
            // 验证更新密钥的必填参数
            requestDto.validateForPut();

            KmsSecretResponseDto responseDto = kmsSecretApplicationService.putSecretValue(requestDto);
            return Result.ok(responseDto);
        } catch (IllegalArgumentException e) {
            log.warn("更新密钥参数校验失败: {}", e.getMessage());
            return Result.fail("参数校验失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("更新密钥值接口调用失败: {}", e.getMessage(), e);
            return Result.fail("更新密钥值失败: " + e.getMessage());
        }
    }
}
