package com.caidaocloud.open.auth.service.interfaces.dto.kms;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * KMS密钥管理通用请求DTO
 * 
 * <AUTHOR>
 * @date 2025/9/5
 */
@Data
public class KmsSecretRequestDto {

    /**
     * 密钥名称
     */
    @NotBlank(message = "密钥名称不能为空")
    @Size(min = 1, max = 192, message = "密钥名称长度必须在1-192个字符之间")
    private String secretName;

    /**
     * 版本ID（创建和更新时必填）
     */
    @Size(min = 1, max = 64, message = "版本ID长度必须在1-64个字符之间")
    private String versionId;

    /**
     * 密钥数据（创建和更新时必填）
     */
    @Size(max = 65536, message = "密钥数据长度不能超过65536个字符")
    private String secretData;

    /**
     * 操作类型枚举
     */
    public enum OperationType {
        CREATE, GET, PUT
    }

    /**
     * 验证创建密钥请求参数
     */
    public void validateForCreate() {
        if (versionId == null || versionId.trim().isEmpty()) {
            throw new IllegalArgumentException("创建密钥时版本ID不能为空");
        }
        if (secretData == null || secretData.trim().isEmpty()) {
            throw new IllegalArgumentException("创建密钥时密钥数据不能为空");
        }
    }

    /**
     * 验证更新密钥请求参数
     */
    public void validateForPut() {
        if (versionId == null || versionId.trim().isEmpty()) {
            throw new IllegalArgumentException("更新密钥时版本ID不能为空");
        }
        if (secretData == null || secretData.trim().isEmpty()) {
            throw new IllegalArgumentException("更新密钥时密钥数据不能为空");
        }
    }
}
